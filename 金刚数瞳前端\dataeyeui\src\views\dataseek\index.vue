<template>
  <div class="dataseek-container">
    <!-- Left Sidebar -->
    <div class="sidebar">
      <el-button type="primary" icon="el-icon-plus" class="new-question-btn" @click="createNewQuestion">新问题</el-button>
      <div class="recent-questions">
        <div class="recent-questions-title">最近问答</div>
        <ul>
          <li v-for="(item, index) in recentQuestions" :key="index" class="recent-question-item" @click="handleQuestionClick(item)">
            {{ item.title }}
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- 对话显示区域 -->
      <transition name="slide-fade" mode="out-in">
        <div v-if="showConversation" key="conversation" class="conversation-area">
          <div class="conversation-header">
            <div class="header-avatar">
              <el-avatar :size="32" style="background-color: #909399;">{{ currentConversation.title.charAt(0) }}</el-avatar>
              <span class="header-title">{{ currentConversation.title }}</span>
            </div>
          </div>

          <div class="conversation-content">
            <!-- 绿色标签区域 -->
            <div class="context-tags">
              <div class="tag-item green-tag">
                <i class="el-icon-edit" />
                <span>输入上下文对话内容</span>
                <i class="el-icon-arrow-down" />
              </div>
              <div class="tag-item green-tag">
                <i class="el-icon-search" />
                <span>搜索问题</span>
                <i class="el-icon-arrow-down" />
              </div>
              <div class="tag-item green-tag">
                <i class="el-icon-search" />
                <span>搜索问题</span>
                <i class="el-icon-arrow-down" />
              </div>
            </div>

            <!-- AI回答区域 -->
            <div class="ai-response-section">
              <!-- 蓝色问题标签 -->
              <div class="question-tag">
                <el-avatar :size="20" style="background-color: #409EFF; margin-right: 8px;">AI</el-avatar>
                <span class="question-text">{{ currentConversation.questionTag }}</span>
                <i class="el-icon-info" style="margin-left: 8px; color: #909399;" />
              </div>

              <!-- 时间戳 -->
              <div class="timestamp">
                {{ currentConversation.timestamp }} {{ currentConversation.question }}
              </div>

              <!-- 回答内容 -->
              <div class="answer-content">
                <div class="answer-title">{{ currentConversation.answerTitle }}</div>
                <div class="answer-value">{{ currentConversation.answerValue }}</div>
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <div class="left-actions">
                  <el-button type="text" icon="el-icon-thumb" size="mini" />
                  <el-button type="text" icon="el-icon-chat-dot-round" size="mini" />
                  <el-button type="text" icon="el-icon-arrow-right" size="mini" />
                </div>
                <div class="right-time">{{ currentConversation.responseTime }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 默认AI问答区域 -->
        <div v-else key="qa-area" class="ai-qa-area">
          <div class="ai-welcome-section">
            <div class="ai-icon">
              <i class="el-icon-cpu" />
            </div>
            <h2 class="ai-title">AI智能问数</h2>
            <p class="ai-subtitle">基于大数据的智能分析助手，为您提供精准的数据洞察</p>
          </div>

          <!-- 快捷问题推荐 -->
          <div class="quick-questions">
            <div class="quick-question-title">推荐问题</div>
            <div class="quick-question-list">
              <div
                v-for="(item, index) in quickQuestions"
                :key="index"
                class="quick-question-item"
                @click="selectQuickQuestion(item)"
              >
                <i :class="item.icon" />
                <span>{{ item.text }}</span>
              </div>
            </div>
          </div>

          <div class="qa-input-container">
            <el-input
              v-model="question"
              :disabled="isLoading"
              placeholder="请输入您的问题，Shift + Enter 换行"
              type="textarea"
              :rows="4"
              class="input-area-dataseek"
              @keydown.enter.shift.prevent="handleSend"
            />
            <el-button
              :loading="isLoading"
              :disabled="!question.trim()"
              class="send-button"
              @click="handleSend"
            >
              <i v-if="!isLoading" class="el-icon-s-promotion" />
            </el-button>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataSeekIndex',
  data() {
    return {
      question: '',
      showConversation: false,
      currentConversation: {},
      isLoading: false,
      quickQuestions: [
        {
          text: '分析去年销售数据趋势',
          icon: 'el-icon-trend-charts'
        },
        {
          text: '查看产品利润率排行',
          icon: 'el-icon-pie-chart'
        },
        {
          text: '对比各地区业绩表现',
          icon: 'el-icon-data-analysis'
        },
        {
          text: '预测下季度销售额',
          icon: 'el-icon-data-line'
        }
      ],
      recentQuestions: [
        {
          title: '去年的产品利润率',
          id: 'profit-rate-1',
          question: '请告诉我去年的产品利润率',
          questionTag: '利润率',
          answerTitle: '利润率',
          answerValue: '0.00%',
          timestamp: '2024.01.01 12:31',
          responseTime: '回答耗时 2024-01-01 07:01'
        },
        {
          title: '你的知识截止到哪天',
          id: 'knowledge-cutoff',
          question: '请告诉我你的知识截止到哪天',
          questionTag: '你的知识截止到哪天',
          answerTitle: '知识截止时间',
          answerValue: '我的知识截止到2024年4月，无法获取之后的最新信息。如果您需要最新的数据或信息，建议您查询最新的资料或咨询相关专业人士。',
          timestamp: '2024.01.01 12:31',
          responseTime: '回答耗时 2024-01-01 07:01'
        },
        {
          title: '去年的产品利润率',
          id: 'profit-rate-2',
          question: '请告诉我去年的产品利润率',
          questionTag: '利润率',
          answerTitle: '利润率',
          answerValue: '15.8%',
          timestamp: '2024.01.01 14:25',
          responseTime: '回答耗时 2024-01-01 09:25'
        }
      ]
    }
  },
  methods: {
    handleQuestionClick(item) {
      this.currentConversation = item
      this.showConversation = true
    },
    backToHome() {
      this.showConversation = false
      this.currentConversation = {}
    },
    createNewQuestion() {
      // 回到默认的AI智能问数页面
      this.showConversation = false
      this.currentConversation = {}
      this.question = '' // 清空输入框
      this.isLoading = false
    },
    selectQuickQuestion(item) {
      this.question = item.text
    },
    async handleSend() {
      if (!this.question.trim() || this.isLoading) return

      this.isLoading = true

      // 模拟API调用
      try {
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 创建新的对话记录
        const newConversation = {
          title: this.question.substring(0, 20) + '...',
          id: 'new-' + Date.now(),
          question: this.question,
          questionTag: this.question.substring(0, 10),
          answerTitle: '分析结果',
          answerValue: '根据您的问题，我正在分析相关数据...',
          timestamp: new Date().toLocaleString(),
          responseTime: '回答耗时 ' + new Date().toLocaleString()
        }

        // 添加到最近问答
        this.recentQuestions.unshift(newConversation)

        // 显示对话
        this.currentConversation = newConversation
        this.showConversation = true
        this.question = ''
      } catch (error) {
        this.$message.error('发送失败，请重试')
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dataseek-container {
  display: flex;
  height: calc(100vh - 50px); // Adjust based on actual header height
  background-color: #ffffff; // White background for the page
}

.sidebar {
  width: 280px; // Fixed width for the sidebar
  background-color: #f8f9fa; // Light grey background for sidebar
  padding: 20px;
  border-right: 1px solid #e0e0e0; // Separator line
  display: flex;
  flex-direction: column;

  .new-question-btn {
    width: 100%;
    margin-bottom: 20px;
  }

  .recent-questions-title {
    font-size: 14px;
    color: #606266; // Subdued text color
    margin-bottom: 10px;
  }

  .recent-questions ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .recent-question-item {
    padding: 10px 5px;
    font-size: 14px;
    color: #303133;
    cursor: pointer;
    border-radius: 4px;
    &:hover {
      background-color: #ecf5ff; // Light blue on hover
    }
  }
}

.main-content {
  flex-grow: 1;
  padding: 40px; // More padding for the main area
  display: flex;
  flex-direction: column;
  align-items: center; // Center content horizontally
  justify-content: center; // Center content vertically
  background-color: #ffffff;
}

// 对话区域样式
.conversation-area {
  width: 100%;
  max-width: 900px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 20px;
}

.conversation-header {
  width: 100%;
  padding: 15px 0;
  margin-bottom: 20px;

  .header-avatar {
    display: flex;
    align-items: center;
    gap: 12px;

    .header-title {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
    }
  }
}

.conversation-content {
  width: 100%;
  flex: 1;
}

// 绿色标签区域
.context-tags {
  margin-bottom: 25px;

  .tag-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    margin-right: 10px;
    margin-bottom: 8px;
    border-radius: 16px;
    font-size: 13px;
    cursor: pointer;

    &.green-tag {
      background-color: #f0f9ff;
      color: #10b981;
      border: 1px solid #10b981;

      i {
        font-size: 12px;
      }

      &:hover {
        background-color: #e6f7ff;
      }
    }
  }
}

// AI回答区域
.ai-response-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

// 蓝色问题标签
.question-tag {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .question-text {
    background-color: #e6f3ff;
    color: #1890ff;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 13px;
    border: 1px solid #1890ff;
  }
}

// 时间戳
.timestamp {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 15px;
  line-height: 1.4;
}

// 回答内容
.answer-content {
  margin-bottom: 20px;

  .answer-title {
    font-size: 14px;
    color: #303133;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .answer-value {
    font-size: 24px;
    color: #303133;
    font-weight: 600;
    line-height: 1.4;
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left-actions {
    display: flex;
    gap: 8px;

    .el-button {
      color: #8c8c8c;
      padding: 6px;
      border: none;
      background: none;

      &:hover {
        color: #1890ff;
        background-color: #f0f8ff;
      }
    }
  }

  .right-time {
    font-size: 11px;
    color: #bfbfbf;
  }
}

.ai-qa-area {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInUp 0.6s ease-out;
}

// 欢迎区域
.ai-welcome-section {
  text-align: center;
  margin-bottom: 40px;

  .ai-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);

    i {
      font-size: 36px;
      color: white;
    }
  }

  .ai-title {
    font-size: 32px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .ai-subtitle {
    font-size: 16px;
    color: #8c8c8c;
    line-height: 1.6;
    margin: 0;
  }
}

// 快捷问题区域
.quick-questions {
  width: 100%;
  margin-bottom: 40px;

  .quick-question-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 16px;
    text-align: center;
  }

  .quick-question-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .quick-question-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 16px;
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      color: #495057;

      i {
        font-size: 16px;
        color: #667eea;
      }

      &:hover {
        background-color: #e3f2fd;
        border-color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
      }
    }
  }
}

.qa-input-container {
  width: 100%;
  position: relative;

  .input-area-dataseek ::v-deep .el-textarea__inner {
    border-radius: 12px;
    padding: 20px;
    padding-right: 70px;
    font-size: 16px;
    min-height: 120px !important;
    border: 2px solid #e9ecef;
    background-color: #ffffff;
    transition: all 0.3s ease;
    resize: none;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    &:disabled {
      background-color: #f8f9fa;
      cursor: not-allowed;
    }
  }

  .send-button {
    position: absolute;
    right: 15px;
    bottom: 15px;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }

    &:disabled {
      background: #d1d5db;
      box-shadow: none;
      cursor: not-allowed;
      transform: none;
    }

    i {
      font-size: 18px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 侧边栏优化
.sidebar {
  .new-question-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }

  .recent-question-item {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background-color: #409EFF;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    &:hover::before {
      transform: scaleY(1);
    }

    &:hover {
      padding-left: 15px;
    }
  }
}

// 过渡动画
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 响应式设计
@media (max-width: 768px) {
  .dataseek-container {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    padding: 15px;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .main-content {
    padding: 20px;
  }

  .ai-qa-area {
    max-width: 100%;
  }

  .quick-question-list {
    grid-template-columns: 1fr;
  }

  .conversation-area {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .ai-welcome-section {
    .ai-icon {
      width: 60px;
      height: 60px;

      i {
        font-size: 28px;
      }
    }

    .ai-title {
      font-size: 24px;
    }

    .ai-subtitle {
      font-size: 14px;
    }
  }

  .qa-input-container {
    .input-area-dataseek ::v-deep .el-textarea__inner {
      min-height: 100px !important;
      padding: 15px;
      padding-right: 60px;
    }

    .send-button {
      width: 40px;
      height: 40px;
      right: 10px;
      bottom: 10px;
    }
  }
}

</style>
