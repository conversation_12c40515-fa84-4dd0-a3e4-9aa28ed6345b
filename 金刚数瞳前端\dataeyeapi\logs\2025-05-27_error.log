2025-05-27 10:17:28.349 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-27 10:17:28.350 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-27 10:17:34.615 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-27 10:17:34.615 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-27 10:17:34.621 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-27 10:17:37.788 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-27 10:17:42.807 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-27 10:17:42.808 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-27 10:20:30.920 | 4faddae8523445febb3c1f2254f92f21 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为50500d95-81ce-4456-afc4-08e5615040a8的会话获取图片验证码成功
2025-05-27 10:20:35.070 | 090226768231406eb2f97090e77d27db | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-27 10:20:36.205 | fd81cb3bcd2346b5b4b756c0a7006c59 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:20:37.092 | 743cc7ef4ad0436795cc43a61e5ddcdf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 10:21:08.938 | 2f0c40c505924825bc345905133f49b4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:21:11.036 | d581a81ab8624b58a0b62d3c6c5bfce0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 10:24:22.161 | 10443da98c4e4015bac1242de742eb7b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:24:23.084 | 84977c3924dc410dbebc89e17c5a8df7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:24:23.818 | 89466f8a3ef04c9ca12f216bfee36df8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 10:24:25.034 | c836e5cde6ea4f6c9d70ebde6b2e8d7c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 10:24:46.125 | f43eb395c922423a8a1c77358ddea9ee | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:24:46.942 | a5387a8970ff495198f77c6661255380 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 10:37:54.612 | 81eba8cbfdf84cfe9580d1572750783e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:37:55.218 | fd29a40604ee4a40a804c6cbe6b4b760 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 10:42:15.333 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-05-27 10:42:15.334 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-05-27 10:43:34.006 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-27 10:43:34.007 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-27 10:43:39.653 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-27 10:43:39.654 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-27 10:43:39.656 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-27 10:43:41.983 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-27 10:43:45.116 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-27 10:43:45.116 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-27 10:43:57.070 | e7c6b2033c4b48a6a91cc8d9322d75c0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 10:43:58.482 | e6e4ae47b42545fa9e03f4489a8b0d0a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 11:18:33.523 | db99adf71779442d84da00716651212e | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为c58a24ed-dd48-4b5e-83b9-18536f4d6d9b的会话获取图片验证码成功
2025-05-27 11:18:56.300 | 82628c67289d4dbebc9adacc17e497a7 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-27 11:18:57.467 | 7bbcd919433c40fb92029f9f4c7c7b74 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 11:18:58.184 | ea0a33ce9a0c4024bca92e726806df79 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 20:41:02.543 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-27 20:41:02.543 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-27 20:41:03.944 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-27 20:41:03.944 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-27 20:41:03.945 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-27 20:41:05.352 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-27 20:41:06.352 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-27 20:41:06.352 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-27 20:41:29.489 | b804efef031f4185a2a4252387c4a36f | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为6e872982-52eb-4059-850c-5ce73a1ef124的会话获取图片验证码成功
2025-05-27 20:41:33.854 | 7aaac6c88f1f4b578a3c262637c26ae7 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-27 20:41:34.227 | c8cfe84ef28a4e38b18ee54b13855007 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 20:41:34.800 | 23dde3a301f7458db76fb8ffd1851165 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 20:41:51.736 | c18ba787a46f4ecfb5cd09862f9dce4d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 20:41:52.173 | 226acc9b588b436cb8f38cd01ff1a12c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 20:45:19.865 | 401ea692d4694fc6b2448c3fd5dc163a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 20:45:20.096 | 078b272d57c14481a43a7b954d5daa10 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-27 21:18:43.793 | c768cfb699d8452ea2e01991a23c6798 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-05-27 21:18:44.313 | 1fbc7e5bd8f7425881fb0e0758842845 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-05-27 21:18:44.373 | 0d64ba47d7b6402c9b36d2cea28ce3c5 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b5331b97-a7a6-4482-9108-59fbef42797b的会话获取图片验证码成功
2025-05-27 21:18:50.071 | 56423dc157684388b88eb77ba172a8a2 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-27 21:18:51.378 | db64339ffd244dce884fec45d1ae6472 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-27 21:18:51.878 | 0f03bc96e73848a4b0a06d5c0cb13d01 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
