2025-05-26 08:51:32.637 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-26 08:51:32.638 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-26 08:51:36.728 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-26 08:51:36.729 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-26 08:51:36.732 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-26 08:51:38.776 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-26 08:51:41.543 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-26 08:51:41.543 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-26 08:52:11.568 | 702c4d17b5804a4fbfa30624f26a17b2 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为739adfc9-fada-4f32-90a9-324ad1cef638的会话获取图片验证码成功
2025-05-26 08:52:17.202 | d830524a07624bbb92522705c36dfa5e | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-26 08:52:18.119 | c549712392aa42008d1aad03b2a40783 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 08:52:18.922 | 163edb88b0d8432fa07cfdf29e8e7acd | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:02:02.903 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-05-26 09:02:02.904 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-05-26 09:02:06.954 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-05-26 09:02:06.955 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-05-26 09:02:06.957 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-05-26 09:02:09.202 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-05-26 09:02:11.812 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-05-26 09:02:11.812 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-05-26 09:06:33.310 | 0d2fc251c73e423bb0d992c02bc9206a | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为93b664bc-a871-49ee-82ec-cf38b9402d77的会话获取图片验证码成功
2025-05-26 09:06:33.571 | d243b3bb192b4be68b6802b57592bf73 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d8c3ae23-84df-44c4-9a93-d8cff023d6c8的会话获取图片验证码成功
2025-05-26 09:06:39.303 | 2e785132bb3d49e18962e625e642d32f | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-26 09:06:40.308 | fff94e16e0604781bf8820e03df5e92f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:06:41.126 | 57ccbc0681d94cac8dd4e21fca05c15a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:10:33.272 | 50a090f88ec8431691ca3c07082a8bac | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:10:35.091 | 931aeedee88d46ff969b66427f76b3be | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:11:52.316 | dfea9cb4840242cf93dffe024cc113a7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:11:53.236 | d64a6be3175e42e5824eea1daf9e4397 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:13:48.247 | ea4686979a604bc48208fc0a6ac6c1b3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:13:49.120 | f91addf1fba84a6c871ceea881424094 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:14:42.816 | 39089f1822f4448a89040c2ee1d6f955 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:14:44.521 | 0f7f99cbf50242f49f9b71a448fd1039 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:15:08.912 | 773298dc9d2648f4a2e0214a2398a43d | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为43687c16-d3fb-43ac-8d81-b7027e5bf4c1的会话获取图片验证码成功
2025-05-26 09:15:46.301 | 55656c1330d94516856d1e772c1fd722 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:15:47.126 | 2cf51f5b3f2048b1a77b87607ca856c1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:40:22.731 | 9f70202db6094d6d97c8a4c2a141e40a | ERROR    | exceptions.handle:exception_handler:70 - (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):

  File "asyncmy\\connection.pyx", line 658, in asyncmy.connection.Connection._read_bytes
    data = await self._reader.readexactly(num_bytes)

  File "E:\python\lib\asyncio\streams.py", line 697, in readexactly
    raise self._exception
          │    └ ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)
          └ <StreamReader exception=ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None) transport=<_SelectorSocketTranspo...

  File "E:\python\lib\asyncio\selector_events.py", line 862, in _read_ready__data_received
    data = self._sock.recv(self.max_size)
           │    │          │    └ 262144
           │    │          └ <_SelectorSocketTransport closed fd=2320>
           │    └ None
           └ <_SelectorSocketTransport closed fd=2320>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000029C75081510>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000029C75431870>
    └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x0000029C75495F30>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>

  File "E:\python\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 92, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x0000029C75496050>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000029C187A6D50>
           └ <_AsyncIoGreenlet object at 0x0000029C197EE040 (otid=0x0000029C749D1A70) dead>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000029C187A6D50>

  File "E:\python\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 104, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'E:\\python\\lib\\site-packages\\asyncmy\\errors.cp310-win_amd64.pyd'>

asyncmy.errors.OperationalError: (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 356
               │     └ 3
               └ <function _main at 0x0000029C714DA710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 356
           │    └ <function BaseProcess._bootstrap at 0x0000029C713F9990>
           └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029C713F9000>
    └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000029C714F9570>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>
    │    └ <function subprocess_started at 0x0000029C73889990>
    └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=380, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000029C714F9690>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=380, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000029C738625F0>
           │       │   └ <uvicorn.server.Server object at 0x0000029C714F9690>
           │       └ <function run at 0x0000029C714E8DC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000029C184061F0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000029C7317A320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029C7317A290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029C7317BD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029C730DB760>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000029C184F17B0>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029C18...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000029C714F9900>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000029C184F17B0>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029C18...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029C18...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000029C18556B00>
          └ <fastapi.applications.FastAPI object at 0x0000029C714F9900>

> File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000029C18573640>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000029C18556A70>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000029C18556B00>

  File "E:\Big model\前端接口\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000029C18786DD0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000029C18555B70>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000029C18556A70>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 24, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000029C18786DD0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000029C1984F5B0>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 44, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000029C1616F520>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000029C1984F5B0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029C18555C30>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000029C1984F5B0>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 83, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000029C1984F5B0>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000029C18555C60>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029C18555C30>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 79, in __call__
    raise exc

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C186A5000>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000029C18555CF0>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000029C18555C60>

  File "E:\python\lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e

  File "E:\python\lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C186A5000>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000029C181A15D0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000029C18555CF0>

  File "E:\python\lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C186A5000>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000029C747CB910>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 276, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C186A5000>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C18787370>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000029C181B88B0>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 66, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x0000029C1984F7F0>
                     └ <function get_request_handler.<locals>.app at 0x0000029C181B8790>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 264, in app
    solved_result = await solve_dependencies(
                          └ <function solve_dependencies at 0x0000029C747C8430>

  File "E:\python\lib\site-packages\fastapi\dependencies\utils.py", line 592, in solve_dependencies
    solved = await call(**sub_values)
                   │      └ {'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJfbmFtZSI6ImFkbWluIiwiZGVwdF9uYW1lIjoiXHU3ODE0XHU1M...
                   └ <bound method LoginService.get_current_user of <class 'module_admin.service.login_service.LoginService'>>

  File "E:\Big model\前端接口\dataeyeapi\module_admin\service\login_service.py", line 212, in get_current_user
    query_user = await UserDao.get_user_by_id(query_db, user_id=token_data.user_id)
                       │       │              │                 │          └ 1
                       │       │              │                 └ TokenData(user_id=1)
                       │       │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000029C1984D4E0>
                       │       └ <classmethod(<function UserDao.get_user_by_id at 0x0000029C17360790>)>
                       └ <class 'module_admin.dao.user_dao.UserDao'>

  File "E:\Big model\前端接口\dataeyeapi\module_admin\dao\user_dao.py", line 89, in get_user_by_id
    await db.execute(
          │  └ <function AsyncSession.execute at 0x0000029C754244C0>
          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000029C1984D4E0>

  File "E:\python\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 455, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x0000029C74A02F80>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x0000029C197EE040 (otid=0x0000029C749D1A70) dead>

  File "E:\python\lib\site-packages\sqlalchemy\orm\session.py", line 2308, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x0000029C7535DAB0>
           └ <sqlalchemy.orm.session.Session object at 0x0000029C1984E170>

  File "E:\python\lib\site-packages\sqlalchemy\orm\session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x0000029C75219000>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "E:\python\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x0000029C74FE1CF0>
             └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x0000029C1984E650>>

  File "E:\python\lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x0000029C74FE1FC0>
           └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x0000029C74FE2170>
          └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 1848, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x0000029C74FE2200>
           └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x0000029C74FE2440>
    └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
          │                    │              └ (<class 'asyncmy.errors.OperationalError'>, OperationalError(2013, 'Lost connection to MySQL server during query ([WinError 1...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ OperationalError("(asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程...

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000029C75081510>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000029C75431870>
    └ <sqlalchemy.engine.base.Connection object at 0x0000029C1984EB00>

  File "E:\python\lib\site-packages\sqlalchemy\engine\default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('0', '0', 1)
    │      │       └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x0000029C75495F30>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>

  File "E:\python\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 92, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('0', '0', 1)
           │    │      │    │              └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x0000029C75496050>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000029C187A6D50>
           └ <_AsyncIoGreenlet object at 0x0000029C197EE040 (otid=0x0000029C749D1A70) dead>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000029C187A6D50>

  File "E:\python\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 104, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('0', '0', 1)
                   │    │               └ 'SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.ema...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000029C19866F20>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 619, in read_packet
    packet_header = await self._read_bytes(4)
  File "asyncmy\\connection.pyx", line 660, in _read_bytes
    raise errors.OperationalError(
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'E:\\python\\lib\\site-packages\\asyncmy\\errors.cp310-win_amd64.pyd'>

sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT DISTINCT sys_user.user_id, sys_user.dept_id, sys_user.user_name, sys_user.nick_name, sys_user.user_type, sys_user.email, sys_user.phonenumber, sys_user.sex, sys_user.avatar, sys_user.password, sys_user.status, sys_user.del_flag, sys_user.login_ip, sys_user.login_date, sys_user.create_by, sys_user.create_time, sys_user.update_by, sys_user.update_time, sys_user.remark 
FROM sys_user 
WHERE sys_user.status = %s AND sys_user.del_flag = %s AND sys_user.user_id = %s]
[parameters: ('0', '0', 1)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-26 09:40:23.789 | 667aee2befcf40e594d59b66ea339e8b | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-05-26 09:40:23.950 | aa0bdcd9a42848c6bb71c9d436f4631e | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为1679a245-68c8-45ec-958a-7bb48773692a的会话获取图片验证码成功
2025-05-26 09:43:26.972 | 7d4794dba26c43b88a2ceca56011903a | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为8f491c35-5f03-4861-8d01-4421070e42d3的会话获取图片验证码成功
2025-05-26 09:44:11.714 | 769437cc857c4f22af8a21390c9f47bd | ERROR    | module_admin.annotation.log_annotation:wrapper:129 - 
Traceback (most recent call last):

  File "E:\python\lib\asyncio\streams.py", line 48, in open_connection
    transport, _ = await loop.create_connection(
                         │    └ <function BaseEventLoop.create_connection at 0x0000029C7317B0A0>
                         └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1036, in create_connection
    infos = await self._ensure_resolved(
                  │    └ <function BaseEventLoop._ensure_resolved at 0x0000029C7317B490>
                  └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1418, in _ensure_resolved
    return await loop.getaddrinfo(host, port, family=family, type=type,
                 │    │           │     │            │            └ <SocketKind.SOCK_STREAM: 1>
                 │    │           │     │            └ 0
                 │    │           │     └ 3306
                 │    │           └ 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com'
                 │    └ <function BaseEventLoop.getaddrinfo at 0x0000029C7317ACB0>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 863, in getaddrinfo
    return await self.run_in_executor(
                 │    └ <function BaseEventLoop.run_in_executor at 0x0000029C7317AB00>
                 └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

asyncio.exceptions.CancelledError


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "E:\python\lib\asyncio\tasks.py", line 456, in wait_for
    return fut.result()
           │   └ <method 'result' of '_asyncio.Task' objects>
           └ <Task cancelled name='Task-65' coro=<open_connection() done, defined at E:\python\lib\asyncio\streams.py:26>>

asyncio.exceptions.CancelledError


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>

  File "E:\python\lib\multiprocessing\spawn.py", line 116, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 356
               │     └ 3
               └ <function _main at 0x0000029C714DA710>

  File "E:\python\lib\multiprocessing\spawn.py", line 129, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 356
           │    └ <function BaseProcess._bootstrap at 0x0000029C713F9990>
           └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>

  File "E:\python\lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029C713F9000>
    └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>

  File "E:\python\lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000029C714F9570>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>
    │    └ <function subprocess_started at 0x0000029C73889990>
    └ <SpawnProcess name='SpawnProcess-1' parent=12960 started>

  File "E:\python\lib\site-packages\uvicorn\_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=380, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000029C714F9690>>

  File "E:\python\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=380, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000029C738625F0>
           │       │   └ <uvicorn.server.Server object at 0x0000029C714F9690>
           │       └ <function run at 0x0000029C714E8DC0>
           └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000029C184061F0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000029C7317A320>
           └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029C7317A290>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029C7317BD90>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>

  File "E:\python\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029C730DB760>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "E:\python\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000029C184F17B0>

  File "E:\python\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029C19...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000029C714F9900>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000029C184F17B0>

  File "E:\python\lib\site-packages\fastapi\applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029C19...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...

  File "E:\python\lib\site-packages\starlette\applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029C19...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000029C18556B00>
          └ <fastapi.applications.FastAPI object at 0x0000029C714F9900>

  File "E:\python\lib\site-packages\starlette\middleware\errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000029C186A4EE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000029...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000029C18556A70>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000029C18556B00>

  File "E:\Big model\前端接口\dataeyeapi\middlewares\trace_middleware\middle.py", line 47, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000029C1989A3B0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000029C18555B70>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000029C18556A70>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 24, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000029C1989A3B0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000029C19885A20>

  File "E:\python\lib\site-packages\starlette\middleware\gzip.py", line 44, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000029C1616F520>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000029C19885A20>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029C18555C30>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000029C19885A20>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'x-forwarded-host': 'localhost:81', 'x-forwarded-proto': 'http', 'x-forwarded-port': '81', 'x-forwarded-for': '127.0...
          │    │               │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000029C19885A20>>
          │    │               │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000029C1616EEF0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029C18555C30>

  File "E:\python\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000029C18555C30...
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000029C18555C60>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000029C18555C30>

  File "E:\python\lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C19AF6B00>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000029C18555CF0>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000029C18555C60>

  File "E:\python\lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C19AF6B00>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000029C181A15D0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000029C18555CF0>

  File "E:\python\lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C19AF6B00>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000029C747CB910>
          └ APIRoute(path='/login', name='login', methods=['POST'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 276, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000029C19AF6B00>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000029C19925090>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000029C181B8820>
          └ APIRoute(path='/login', name='login', methods=['POST'])

  File "E:\python\lib\site-packages\starlette\routing.py", line 66, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x0000029C19884B20>
                     └ <function get_request_handler.<locals>.app at 0x0000029C181B8700>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 274, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000029C747CA3B0>

  File "E:\python\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'form_data': <module_admin.service.login_service.CustomOAuth2PasswordRequestForm object at 0x0000029C1876DE70>, 'query_db': ...
                 │         └ <function login at 0x0000029C17988550>
                 └ <fastapi.dependencies.models.Dependant object at 0x0000029C181A2770>

> File "E:\Big model\前端接口\dataeyeapi\module_admin\annotation\log_annotation.py", line 121, in wrapper
    result = await func(*args, **kwargs)
                   │     │       └ {'form_data': <module_admin.service.login_service.CustomOAuth2PasswordRequestForm object at 0x0000029C1876DE70>, 'query_db': ...
                   │     └ ()
                   └ <function login at 0x0000029C179884C0>

  File "E:\Big model\前端接口\dataeyeapi\module_admin\controller\login_controller.py", line 42, in login
    result = await LoginService.authenticate_user(request, query_db, user)
                   │            │                 │        │         └ UserLogin(user_name='admin', password='admin123', code='6', uuid='8f491c35-5f03-4861-8d01-4421070e42d3', login_info={'ipaddr'...
                   │            │                 │        └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000029C187B2DA0>
                   │            │                 └ <starlette.requests.Request object at 0x0000029C19884B20>
                   │            └ <classmethod(<function LoginService.authenticate_user at 0x0000029C174B01F0>)>
                   └ <class 'module_admin.service.login_service.LoginService'>

  File "E:\Big model\前端接口\dataeyeapi\module_admin\service\login_service.py", line 96, in authenticate_user
    user = await login_by_account(query_db, login_user.user_name)
                 │                │         │          └ 'admin'
                 │                │         └ UserLogin(user_name='admin', password='admin123', code='6', uuid='8f491c35-5f03-4861-8d01-4421070e42d3', login_info={'ipaddr'...
                 │                └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000029C187B2DA0>
                 └ <function login_by_account at 0x0000029C16196680>

  File "E:\Big model\前端接口\dataeyeapi\module_admin\dao\login_dao.py", line 16, in login_by_account
    await db.execute(
          │  └ <function AsyncSession.execute at 0x0000029C754244C0>
          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000029C187B2DA0>

  File "E:\python\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 455, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x0000029C74A02F80>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x0000029C19862AC0 (otid=0x0000029C749D1A70) dead>

  File "E:\python\lib\site-packages\sqlalchemy\orm\session.py", line 2308, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x0000029C7535DAB0>
           └ <sqlalchemy.orm.session.Session object at 0x0000029C198860E0>

  File "E:\python\lib\site-packages\sqlalchemy\orm\session.py", line 2180, in _execute_internal
    conn = self._connection_for_bind(bind)
           │    │                    └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)
           │    └ <function Session._connection_for_bind at 0x0000029C7535DA20>
           └ <sqlalchemy.orm.session.Session object at 0x0000029C198860E0>

  File "E:\python\lib\site-packages\sqlalchemy\orm\session.py", line 2047, in _connection_for_bind
    return trans._connection_for_bind(engine, execution_options)
           │     │                    │       └ None
           │     │                    └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)
           │     └ <function SessionTransaction._connection_for_bind at 0x0000029C7535C790>
           └ <sqlalchemy.orm.session.SessionTransaction object at 0x0000029C1808CA40>

  File "<string>", line 2, in _connection_for_bind

  File "E:\python\lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                │  │      │      └ {}
                │  │      └ (Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye), None)
                │  └ <sqlalchemy.orm.session.SessionTransaction object at 0x0000029C1808CA40>
                └ <function SessionTransaction._connection_for_bind at 0x0000029C7535C670>

  File "E:\python\lib\site-packages\sqlalchemy\orm\session.py", line 1143, in _connection_for_bind
    conn = bind.connect()
           │    └ <function Engine.connect at 0x0000029C74FE4430>
           └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 3268, in connect
    return self._connection_cls(self)
           │    │               └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)
           │    └ <class 'sqlalchemy.engine.base.Connection'>
           └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 145, in __init__
    self._dbapi_connection = engine.raw_connection()
    │                        │      └ <function Engine.raw_connection at 0x0000029C74FE44C0>
    │                        └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)
    └ <sqlalchemy.engine.base.Connection object at 0x0000029C198863E0>

  File "E:\python\lib\site-packages\sqlalchemy\engine\base.py", line 3292, in raw_connection
    return self.pool.connect()
           │    │    └ <function Pool.connect at 0x0000029C74AA0EE0>
           │    └ <sqlalchemy.pool.impl.AsyncAdaptedQueuePool object at 0x0000029C750BC040>
           └ Engine(mysql+asyncmy://dataeye:***@rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com:3306/jgdataeye)

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 452, in connect
    return _ConnectionFairy._checkout(self)
           │                │         └ <sqlalchemy.pool.impl.AsyncAdaptedQueuePool object at 0x0000029C750BC040>
           │                └ <classmethod(<function _ConnectionFairy._checkout at 0x0000029C74AA27A0>)>
           └ <class 'sqlalchemy.pool.base._ConnectionFairy'>

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 1269, in _checkout
    fairy = _ConnectionRecord.checkout(pool)
            │                 │        └ <sqlalchemy.pool.impl.AsyncAdaptedQueuePool object at 0x0000029C750BC040>
            │                 └ <classmethod(<function _ConnectionRecord.checkout at 0x0000029C74AA1900>)>
            └ <class 'sqlalchemy.pool.base._ConnectionRecord'>

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 721, in checkout
    with util.safe_reraise():
         │    └ <class 'sqlalchemy.util.langhelpers.safe_reraise'>
         └ <module 'sqlalchemy.util' from 'E:\\python\\lib\\site-packages\\sqlalchemy\\util\\__init__.py'>

  File "E:\python\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
          │         │              └ <traceback object at 0x0000029C19C0C180>
          │         └ <method 'with_traceback' of 'BaseException' objects>
          └ TimeoutError()

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 719, in checkout
    dbapi_connection = rec.get_connection()
                       │   └ <function _ConnectionRecord.get_connection at 0x0000029C74AA1CF0>
                       └ <sqlalchemy.pool.base._ConnectionRecord object at 0x0000029C185FA200>

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 868, in get_connection
    self.__connect()
    └ <sqlalchemy.pool.base._ConnectionRecord object at 0x0000029C185FA200>

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 902, in __connect
    with util.safe_reraise():
         │    └ <class 'sqlalchemy.util.langhelpers.safe_reraise'>
         └ <module 'sqlalchemy.util' from 'E:\\python\\lib\\site-packages\\sqlalchemy\\util\\__init__.py'>

  File "E:\python\lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
          │         │              └ <traceback object at 0x0000029C18125500>
          │         └ <method 'with_traceback' of 'BaseException' objects>
          └ TimeoutError()

  File "E:\python\lib\site-packages\sqlalchemy\pool\base.py", line 898, in __connect
    self.dbapi_connection = connection = pool._invoke_creator(self)
    │    │                               │    │               └ <sqlalchemy.pool.base._ConnectionRecord object at 0x0000029C185FA200>
    │    │                               │    └ <function create_engine.<locals>.connect at 0x0000029C748EB9A0>
    │    │                               └ <sqlalchemy.pool.impl.AsyncAdaptedQueuePool object at 0x0000029C750BC040>
    │    └ <member 'dbapi_connection' of '_ConnectionRecord' objects>
    └ <sqlalchemy.pool.base._ConnectionRecord object at 0x0000029C185FA200>

  File "E:\python\lib\site-packages\sqlalchemy\engine\create.py", line 637, in connect
    return dialect.connect(*cargs, **cparams)
           │       │        │        └ {'host': 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com', 'db': 'jgdataeye', 'user': 'dataeye', 'password': 'jg@050877', 'por...
           │       │        └ []
           │       └ <function DefaultDialect.connect at 0x0000029C750809D0>
           └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000029C75431870>

  File "E:\python\lib\site-packages\sqlalchemy\engine\default.py", line 616, in connect
    return self.loaded_dbapi.connect(*cargs, **cparams)
           │    │            │        │        └ {'host': 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com', 'db': 'jgdataeye', 'user': 'dataeye', 'password': 'jg@050877', 'por...
           │    │            │        └ ()
           │    │            └ <function AsyncAdapt_asyncmy_dbapi.connect at 0x0000029C75496EF0>
           │    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_dbapi object at 0x0000029C75431750>
           └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000029C75431870>

  File "E:\python\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 281, in connect
    await_only(creator_fn(*arg, **kw)),
    │          │           │      └ {'host': 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com', 'db': 'jgdataeye', 'user': 'dataeye', 'password': 'jg@050877', 'por...
    │          │           └ ()
    │          └ <cyfunction connect at 0x0000029C7562A260>
    └ <function await_only at 0x0000029C74A02E60>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 125, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <asyncmy.contexts._ConnectionContextManager object at 0x0000029C197E7480>
           └ <_AsyncIoGreenlet object at 0x0000029C19862AC0 (otid=0x0000029C749D1A70) dead>

  File "E:\python\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 185, in greenlet_spawn
    value = await result
                  └ <asyncmy.contexts._ConnectionContextManager object at 0x0000029C197E7480>

  File "asyncmy\\connection.pyx", line 1345, in _connect
    await conn.connect()
  File "asyncmy\\connection.pyx", line 596, in connect
    raise e
  File "asyncmy\\connection.pyx", line 558, in asyncmy.connection.Connection.connect
    self._reader, self._writer = await asyncio.wait_for(asyncio.open_connection(
                                       │       │        │       └ <function open_connection at 0x0000029C7317D000>
                                       │       │        └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>
                                       │       └ <function wait_for at 0x0000029C7315D240>
                                       └ <module 'asyncio' from 'E:\\python\\lib\\asyncio\\__init__.py'>

  File "E:\python\lib\asyncio\tasks.py", line 458, in wait_for
    raise exceptions.TimeoutError() from exc
          │          └ <class 'asyncio.exceptions.TimeoutError'>
          └ <module 'asyncio.exceptions' from 'E:\\python\\lib\\asyncio\\exceptions.py'>

asyncio.exceptions.TimeoutError
2025-05-26 09:44:12.030 | a3d8ca260b3540e19dd253d578ffe4f3 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为d11589cb-1d40-490e-92c1-9291e45c7150的会话获取图片验证码成功
2025-05-26 09:44:19.021 | 17f6ca03efd04cb1ae8ffbe67992871b | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-05-26 09:44:20.234 | 68fb0038e9ad45e38f2ce427a56e4309 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:44:21.105 | 49f31ca7c7644022a5e641ae705376ec | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 09:49:07.957 | dea74fa6642141788ea6b7277f62d43c | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-05-26 09:49:08.335 | 1ec0065c77a2475f827974d136a47407 | INFO     | module_admin.controller.role_controller:get_system_role_list:53 - 获取成功
2025-05-26 09:49:09.235 | 5991a82485a04f74a8eb968f5c858d3f | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-05-26 09:49:09.355 | 2987041062f04e45b75bd2ab175e9c7f | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-05-26 09:49:10.206 | f9a98ab058d3419ba68e1202a5fc3bdc | INFO     | module_admin.controller.dept_controller:get_system_dept_list:50 - 获取成功
2025-05-26 09:50:08.014 | 2203db67688245b992b8f414f43dda66 | INFO     | module_admin.controller.role_controller:get_system_role_list:53 - 获取成功
2025-05-26 09:52:51.581 | 5c2b8671465f4c8bbd63e56b65a4eb02 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 09:52:52.225 | 22b92c91bc9d4085be1d143be7867b2c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-05-26 10:02:39.154 | c883bd63260e4fe0ac7f3d2b597b7c5a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-05-26 10:02:39.467 | 52ea0c5bc205406fb531f8aaa629eda9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
