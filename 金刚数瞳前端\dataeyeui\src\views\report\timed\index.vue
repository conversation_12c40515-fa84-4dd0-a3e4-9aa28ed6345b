<template>
  <div class="app-container">
    <div class="page-header">
      <h2 class="page-title">定时提醒</h2>
      <p class="page-description">管理报告的定时提醒和自动推送</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" icon="el-icon-plus" @click="createReminder">新建提醒</el-button>
        <el-button icon="el-icon-refresh" @click="refreshList">刷新</el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索提醒..."
          prefix-icon="el-icon-search"
          style="width: 300px;"
          clearable
        />
        <el-select v-model="filterStatus" placeholder="状态筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="启用" value="enabled" />
          <el-option label="禁用" value="disabled" />
          <el-option label="已过期" value="expired" />
        </el-select>
      </div>
    </div>

    <!-- 提醒列表 -->
    <div class="reminder-list">
      <el-table :data="filteredReminders" style="width: 100%">
        <el-table-column width="60">
          <template slot-scope="scope">
            <div class="reminder-icon">
              <i class="el-icon-alarm-clock"></i>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="提醒名称" min-width="200">
          <template slot-scope="scope">
            <div class="reminder-info">
              <div class="reminder-name">{{ scope.row.name }}</div>
              <div class="reminder-description">{{ scope.row.description }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="reportTitle" label="关联报告" min-width="150" />

        <el-table-column prop="frequency" label="频率" width="120">
          <template slot-scope="scope">
            <el-tag size="small" :type="getFrequencyTagType(scope.row.frequency)">
              {{ getFrequencyText(scope.row.frequency) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="nextRun" label="下次执行" width="150" />

        <el-table-column prop="recipients" label="接收人" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.recipients.length }} 人</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="toggleReminder(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editReminder(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="testReminder(scope.row)">测试</el-button>
            <el-button type="text" size="small" @click="viewLogs(scope.row)">日志</el-button>
            <el-button type="text" size="small" @click="deleteReminder(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 创建/编辑提醒对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="reminderDialogVisible" width="700px">
      <el-form :model="reminderForm" :rules="reminderRules" ref="reminderForm" label-width="100px">
        <el-form-item label="提醒名称" prop="name">
          <el-input v-model="reminderForm.name" placeholder="输入提醒名称" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="reminderForm.description"
            type="textarea"
            :rows="2"
            placeholder="输入提醒描述"
          />
        </el-form-item>

        <el-form-item label="关联报告" prop="reportId">
          <el-select v-model="reminderForm.reportId" placeholder="选择报告" style="width: 100%;">
            <el-option
              v-for="report in availableReports"
              :key="report.id"
              :label="report.title"
              :value="report.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="提醒频率" prop="frequency">
          <el-select v-model="reminderForm.frequency" placeholder="选择频率" style="width: 100%;">
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="reminderForm.frequency === 'custom'" label="Cron表达式" prop="cronExpression">
          <el-input v-model="reminderForm.cronExpression" placeholder="0 9 * * 1-5" />
          <div class="form-help">例如：0 9 * * 1-5 表示工作日上午9点</div>
        </el-form-item>

        <el-form-item label="执行时间" prop="executeTime">
          <el-time-picker
            v-model="reminderForm.executeTime"
            placeholder="选择执行时间"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="接收人" prop="recipients">
          <el-select
            v-model="reminderForm.recipients"
            multiple
            placeholder="选择接收人"
            style="width: 100%;"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="user.name"
              :value="user.email"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="推送方式">
          <el-checkbox-group v-model="reminderForm.pushMethods">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="webhook">Webhook</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="有效期" prop="expireDate">
          <el-date-picker
            v-model="reminderForm.expireDate"
            type="date"
            placeholder="选择过期日期"
            style="width: 100%;"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="reminderDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveReminder">保存</el-button>
      </div>
    </el-dialog>

    <!-- 执行日志对话框 -->
    <el-dialog title="执行日志" :visible.sync="logsDialogVisible" width="800px">
      <el-table :data="currentLogs" style="width: 100%">
        <el-table-column prop="executeTime" label="执行时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recipients" label="接收人数" width="100" />
        <el-table-column prop="message" label="消息" min-width="200" />
        <el-table-column prop="duration" label="耗时" width="100" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ReportTimed',
  data() {
    return {
      searchKeyword: '',
      filterStatus: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      reminderDialogVisible: false,
      logsDialogVisible: false,
      isEditing: false,
      reminderForm: {
        id: null,
        name: '',
        description: '',
        reportId: '',
        frequency: 'daily',
        cronExpression: '',
        executeTime: '09:00',
        recipients: [],
        pushMethods: ['email'],
        expireDate: null,
        enabled: true
      },
      reminderRules: {
        name: [
          { required: true, message: '请输入提醒名称', trigger: 'blur' }
        ],
        reportId: [
          { required: true, message: '请选择关联报告', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '请选择提醒频率', trigger: 'change' }
        ],
        recipients: [
          { required: true, message: '请选择接收人', trigger: 'change' }
        ]
      },
      reminders: [
        {
          id: 1,
          name: '销售日报提醒',
          description: '每日销售数据报告推送',
          reportId: 1,
          reportTitle: '销售数据分析报告',
          frequency: 'daily',
          executeTime: '09:00',
          nextRun: '2024-01-16 09:00',
          recipients: ['<EMAIL>', '<EMAIL>'],
          pushMethods: ['email'],
          enabled: true,
          createTime: '2024-01-10'
        },
        {
          id: 2,
          name: '周度财务汇总',
          description: '每周财务数据汇总报告',
          reportId: 2,
          reportTitle: '财务分析报告',
          frequency: 'weekly',
          executeTime: '10:00',
          nextRun: '2024-01-22 10:00',
          recipients: ['<EMAIL>'],
          pushMethods: ['email', 'sms'],
          enabled: true,
          createTime: '2024-01-08'
        }
      ],
      availableReports: [
        { id: 1, title: '销售数据分析报告' },
        { id: 2, title: '财务分析报告' },
        { id: 3, title: '用户行为分析报告' }
      ],
      availableUsers: [
        { id: 1, name: '张三', email: '<EMAIL>' },
        { id: 2, name: '李四', email: '<EMAIL>' },
        { id: 3, name: '王五', email: '<EMAIL>' }
      ],
      currentLogs: [
        {
          executeTime: '2024-01-15 09:00:00',
          status: 'success',
          recipients: 2,
          message: '邮件发送成功',
          duration: '2.3s'
        },
        {
          executeTime: '2024-01-14 09:00:00',
          status: 'success',
          recipients: 2,
          message: '邮件发送成功',
          duration: '1.8s'
        },
        {
          executeTime: '2024-01-13 09:00:00',
          status: 'failed',
          recipients: 0,
          message: '邮件服务器连接失败',
          duration: '30s'
        }
      ]
    }
  },
  computed: {
    dialogTitle() {
      return this.isEditing ? '编辑提醒' : '新建提醒'
    },
    filteredReminders() {
      let filtered = this.reminders

      // 状态筛选
      if (this.filterStatus) {
        if (this.filterStatus === 'enabled') {
          filtered = filtered.filter(reminder => reminder.enabled)
        } else if (this.filterStatus === 'disabled') {
          filtered = filtered.filter(reminder => !reminder.enabled)
        } else if (this.filterStatus === 'expired') {
          // 这里可以添加过期逻辑
          filtered = filtered.filter(reminder => false) // 暂时没有过期的
        }
      }

      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(reminder =>
          reminder.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          reminder.description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          reminder.reportTitle.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }

      this.total = filtered.length
      return filtered
    }
  },
  methods: {
    getFrequencyTagType(frequency) {
      const types = {
        daily: 'success',
        weekly: 'warning',
        monthly: 'info',
        custom: 'primary'
      }
      return types[frequency] || 'info'
    },
    getFrequencyText(frequency) {
      const texts = {
        daily: '每日',
        weekly: '每周',
        monthly: '每月',
        custom: '自定义'
      }
      return texts[frequency] || '未知'
    },
    createReminder() {
      this.isEditing = false
      this.resetForm()
      this.reminderDialogVisible = true
    },
    editReminder(reminder) {
      this.isEditing = true
      this.reminderForm = { ...reminder }
      this.reminderDialogVisible = true
    },
    resetForm() {
      this.reminderForm = {
        id: null,
        name: '',
        description: '',
        reportId: '',
        frequency: 'daily',
        cronExpression: '',
        executeTime: '09:00',
        recipients: [],
        pushMethods: ['email'],
        expireDate: null,
        enabled: true
      }
    },
    saveReminder() {
      this.$refs.reminderForm.validate((valid) => {
        if (valid) {
          if (this.isEditing) {
            // 更新现有提醒
            const index = this.reminders.findIndex(r => r.id === this.reminderForm.id)
            if (index > -1) {
              this.reminders.splice(index, 1, { ...this.reminderForm })
            }
            this.$message.success('提醒更新成功')
          } else {
            // 创建新提醒
            const newReminder = {
              ...this.reminderForm,
              id: Date.now(),
              reportTitle: this.availableReports.find(r => r.id === this.reminderForm.reportId)?.title || '',
              nextRun: this.calculateNextRun(),
              createTime: new Date().toISOString().split('T')[0]
            }
            this.reminders.push(newReminder)
            this.$message.success('提醒创建成功')
          }
          this.reminderDialogVisible = false
        }
      })
    },
    calculateNextRun() {
      // 简单的下次执行时间计算
      const now = new Date()
      const tomorrow = new Date(now)
      tomorrow.setDate(tomorrow.getDate() + 1)
      return tomorrow.toISOString().split('T')[0] + ' ' + this.reminderForm.executeTime
    },
    toggleReminder(reminder) {
      this.$message.success(reminder.enabled ? '提醒已启用' : '提醒已禁用')
    },
    testReminder(reminder) {
      this.$message.info(`正在测试提醒: ${reminder.name}`)
    },
    viewLogs(reminder) {
      this.logsDialogVisible = true
    },
    deleteReminder(reminder) {
      this.$confirm(`确定要删除提醒"${reminder.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reminders.findIndex(r => r.id === reminder.id)
        if (index > -1) {
          this.reminders.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    refreshList() {
      this.$message.success('列表已刷新')
    },
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;

  .page-title {
    font-size: 24px;
    color: #303133;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #909399;
    margin: 0;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
  }
}

.reminder-list {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .reminder-icon {
    width: 32px;
    height: 32px;
    background: #E6A23C;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
  }

  .reminder-info {
    .reminder-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }

    .reminder-description {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
