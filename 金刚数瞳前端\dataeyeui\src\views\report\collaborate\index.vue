<template>
  <div class="app-container">
    <div class="page-header">
      <h2 class="page-title">协作报告</h2>
      <p class="page-description">查看和管理与他人协作的报告</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="showCreateDialog">新建</el-button>
        <el-button size="small" @click="showTemplateLibrary">模板库</el-button>
        <el-button size="small" @click="showTagManageDialog">标签管理</el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索协作报告..."
          prefix-icon="el-icon-search"
          style="width: 300px;"
          clearable
        />
        <el-select v-model="filterRole" placeholder="角色筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="所有者" value="owner" />
          <el-option label="编辑者" value="editor" />
          <el-option label="查看者" value="viewer" />
        </el-select>
      </div>
    </div>

    <!-- 协作报告列表 -->
    <div class="collaborate-list">
      <el-row :gutter="20">
        <el-col
          v-for="report in filteredReports"
          :key="report.id"
          :xs="24"
          :sm="12"
          :md="8"
          :lg="6"
          class="report-item-col"
        >
          <div class="report-card" @click="openReport(report)">
            <div class="card-header">
              <div class="report-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="role-badge">
                <el-tag
                  :type="getRoleTagType(report.myRole)"
                  size="mini"
                >
                  {{ getRoleText(report.myRole) }}
                </el-tag>
              </div>
            </div>
            <div class="card-content">
              <h3 class="report-title">{{ report.title }}</h3>
              <p class="report-description">{{ report.description }}</p>
              <div class="collaborators">
                <div class="collaborator-avatars">
                  <el-avatar
                    v-for="(collaborator, index) in report.collaborators.slice(0, 3)"
                    :key="collaborator.id"
                    :size="24"
                    :style="{ marginLeft: index > 0 ? '-8px' : '0', zIndex: 3 - index }"
                    :src="collaborator.avatar"
                  >
                    {{ collaborator.name.charAt(0) }}
                  </el-avatar>
                  <span v-if="report.collaborators.length > 3" class="more-count">
                    +{{ report.collaborators.length - 3 }}
                  </span>
                </div>
                <div class="last-activity">
                  <span class="activity-text">{{ report.lastActivity }}</span>
                  <span class="activity-time">{{ report.lastActivityTime }}</span>
                </div>
              </div>
            </div>
            <div class="card-actions">
              <el-button
                type="text"
                size="small"
                @click.stop="manageCollaborators(report)"
                v-if="report.myRole === 'owner'"
              >
                管理协作
              </el-button>
              <el-button type="text" size="small" @click.stop="shareReport(report)">分享</el-button>
              <el-dropdown trigger="click" @command="handleCommand">
                <el-button type="text" size="small">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{action: 'copy', report}">复制链接</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'export', report}">导出</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'leave', report}" v-if="report.myRole !== 'owner'" divided>
                    退出协作
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredReports.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="el-icon-user"></i>
      </div>
      <h3>暂无协作报告</h3>
      <p>您还没有参与任何协作报告，<a href="#" @click="inviteCollaborator">邀请他人协作</a>或等待他人邀请您。</p>
    </div>

    <!-- 新建报告对话框 -->
    <el-dialog title="新建报告" :visible.sync="createDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form :model="createForm" :rules="createRules" ref="createForm" label-width="60px">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入报告名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入报告简介"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input
            v-model="createForm.tags"
            placeholder="请输入标签信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelCreate">取消</el-button>
        <el-button type="primary" @click="confirmCreate">确定</el-button>
      </div>
    </el-dialog>

    <!-- 标签管理对话框 -->
    <el-dialog title="标签管理" :visible.sync="tagManageDialogVisible" width="600px" :close-on-click-modal="false">
      <div class="tag-manage-content">
        <!-- 搜索框 -->
        <div class="tag-search">
          <el-input
            v-model="tagSearchKeyword"
            placeholder="输入关键词搜索"
            prefix-icon="el-icon-search"
            size="small"
            style="width: 300px;"
            clearable
          />
        </div>

        <!-- 标签表格 -->
        <div class="tag-table">
          <el-table :data="filteredTags" style="width: 100%" size="small">
            <el-table-column prop="name" label="标签名" min-width="120" />
            <el-table-column prop="reportCount" label="关联报告数" width="120" align="center" />
            <el-table-column prop="modifyType" label="修改方式" width="100" align="center" />
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="editTag(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="deleteTag(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTags.length === 0" class="tag-empty-state">
          <div class="empty-icon">
            <i class="el-icon-price-tag"></i>
          </div>
          <p>暂无数据</p>
        </div>
      </div>
    </el-dialog>

    <!-- 模板库抽屉 -->
    <el-drawer
      title="模板库"
      :visible.sync="templateLibraryVisible"
      direction="btt"
      size="90%"
      :close-on-click-modal="false"
      class="template-library-drawer"
    >
      <div class="template-library-content">
        <!-- 左侧模板列表 -->
        <div class="template-sidebar">
          <div class="template-search">
            <el-input
              v-model="templateSearchKeyword"
              placeholder="输入关键词搜索"
              prefix-icon="el-icon-search"
              size="small"
              clearable
            />
          </div>

          <div class="template-categories">
            <div class="category-title">模板分类</div>
            <div class="template-list">
              <div
                v-for="template in filteredTemplates"
                :key="template.id"
                :class="['template-item', { active: selectedTemplate && selectedTemplate.id === template.id }]"
                @click="selectTemplate(template)"
              >
                <div class="template-name">
                  {{ template.name }}
                  <el-tag v-if="template.isNew" type="primary" size="mini">新</el-tag>
                </div>
                <div class="template-desc">{{ template.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧模板预览 -->
        <div class="template-preview">
          <div v-if="selectedTemplate" class="preview-content">
            <div class="preview-header">
              <h3>{{ selectedTemplate.name }}</h3>
              <el-button type="primary" size="small" @click="useTemplate">使用模板</el-button>
            </div>
            <div class="preview-body">
              <div class="template-preview-image">
                <img :src="selectedTemplate.previewImage" :alt="selectedTemplate.name">
              </div>
            </div>
          </div>
          <div v-else class="preview-empty">
            <div class="empty-icon">
              <i class="el-icon-document"></i>
            </div>
            <p>请选择一个模板查看预览</p>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 邀请协作对话框 -->
    <el-dialog title="邀请协作" :visible.sync="inviteDialogVisible" width="500px">
      <el-form :model="inviteForm" label-width="80px">
        <el-form-item label="报告">
          <el-select v-model="inviteForm.reportId" placeholder="选择要协作的报告" style="width: 100%;">
            <el-option
              v-for="report in myReports"
              :key="report.id"
              :label="report.title"
              :value="report.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="inviteForm.email" placeholder="输入协作者邮箱" />
        </el-form-item>
        <el-form-item label="权限">
          <el-select v-model="inviteForm.role" placeholder="选择权限" style="width: 100%;">
            <el-option label="查看者" value="viewer" />
            <el-option label="编辑者" value="editor" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息">
          <el-input
            v-model="inviteForm.message"
            type="textarea"
            :rows="3"
            placeholder="邀请消息（可选）"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="inviteDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sendInvite">发送邀请</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ReportCollaborate',
  data() {
    return {
      searchKeyword: '',
      filterRole: '',
      createDialogVisible: false,
      createForm: {
        name: '',
        description: '',
        tags: ''
      },
      createRules: {
        name: [
          { required: true, message: '请输入报告名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      tagManageDialogVisible: false,
      tagSearchKeyword: '',
      tags: [],
      templateLibraryVisible: false,
      templateSearchKeyword: '',
      selectedTemplate: null,
      templates: [
        {
          id: 1,
          name: '人工智能发展趋势分析报告1',
          description: '一个AI报告',
          isNew: true,
          previewImage: '/static/template-preview-placeholder.svg',
          category: 'AI分析'
        },
        {
          id: 2,
          name: '人工智能发展趋势',
          description: 'AI发展趋势分析',
          isNew: false,
          previewImage: '/static/template-preview-placeholder.svg',
          category: 'AI分析'
        },
        {
          id: 3,
          name: '对于AI',
          description: 'AI相关报告模板',
          isNew: false,
          previewImage: '/static/template-preview-placeholder.svg',
          category: 'AI分析'
        }
      ],
      inviteDialogVisible: false,
      inviteForm: {
        reportId: '',
        email: '',
        role: 'viewer',
        message: ''
      },
      reports: [
        {
          id: 1,
          title: '市场分析报告',
          description: '2024年市场趋势分析与预测',
          myRole: 'owner',
          lastActivity: '张三编辑了图表',
          lastActivityTime: '2小时前',
          collaborators: [
            { id: 1, name: '张三', avatar: '', role: 'editor' },
            { id: 2, name: '李四', avatar: '', role: 'viewer' },
            { id: 3, name: '王五', avatar: '', role: 'editor' }
          ]
        },
        {
          id: 2,
          title: '财务季度报告',
          description: 'Q1财务数据汇总与分析',
          myRole: 'editor',
          lastActivity: '李四添加了评论',
          lastActivityTime: '1天前',
          collaborators: [
            { id: 4, name: '李四', avatar: '', role: 'owner' },
            { id: 5, name: '赵六', avatar: '', role: 'viewer' }
          ]
        },
        {
          id: 3,
          title: '用户体验研究',
          description: '产品用户体验调研报告',
          myRole: 'viewer',
          lastActivity: '王五更新了数据',
          lastActivityTime: '3天前',
          collaborators: [
            { id: 6, name: '王五', avatar: '', role: 'owner' },
            { id: 7, name: '孙七', avatar: '', role: 'editor' },
            { id: 8, name: '周八', avatar: '', role: 'viewer' },
            { id: 9, name: '吴九', avatar: '', role: 'viewer' }
          ]
        }
      ],
      myReports: [
        { id: 1, title: '市场分析报告' },
        { id: 4, title: '产品销售报告' },
        { id: 5, title: '客户满意度调查' }
      ]
    }
  },
  computed: {
    filteredReports() {
      let filtered = this.reports

      // 角色筛选
      if (this.filterRole) {
        filtered = filtered.filter(report => report.myRole === this.filterRole)
      }

      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(report =>
          report.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          report.description.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }

      return filtered
    },
    filteredTags() {
      if (!this.tagSearchKeyword) {
        return this.tags
      }
      return this.tags.filter(tag =>
        tag.name.toLowerCase().includes(this.tagSearchKeyword.toLowerCase())
      )
    },
    filteredTemplates() {
      if (!this.templateSearchKeyword) {
        return this.templates
      }
      return this.templates.filter(template =>
        template.name.toLowerCase().includes(this.templateSearchKeyword.toLowerCase()) ||
        template.description.toLowerCase().includes(this.templateSearchKeyword.toLowerCase())
      )
    }
  },
  methods: {
    getRoleTagType(role) {
      const types = {
        owner: 'success',
        editor: 'warning',
        viewer: 'info'
      }
      return types[role] || 'info'
    },
    getRoleText(role) {
      const texts = {
        owner: '所有者',
        editor: '编辑者',
        viewer: '查看者'
      }
      return texts[role] || '未知'
    },
    showCreateDialog() {
      this.createDialogVisible = true
    },
    cancelCreate() {
      this.createDialogVisible = false
      this.resetCreateForm()
    },
    confirmCreate() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 创建新报告
          const newReport = {
            id: Date.now(),
            title: this.createForm.name,
            description: this.createForm.description,
            myRole: 'owner',
            lastActivity: '创建了报告',
            lastActivityTime: '刚刚',
            collaborators: [
              { id: 1, name: '当前用户', avatar: '', role: 'owner' }
            ]
          }
          this.reports.unshift(newReport)
          this.$message.success('报告创建成功')
          this.createDialogVisible = false
          this.resetCreateForm()
        }
      })
    },
    resetCreateForm() {
      this.createForm = {
        name: '',
        description: '',
        tags: ''
      }
      this.$nextTick(() => {
        this.$refs.createForm && this.$refs.createForm.clearValidate()
      })
    },
    showTagManageDialog() {
      this.tagManageDialogVisible = true
    },
    showTemplateLibrary() {
      this.templateLibraryVisible = true
      // 默认选择第一个模板
      if (this.templates.length > 0) {
        this.selectedTemplate = this.templates[0]
      }
    },
    selectTemplate(template) {
      this.selectedTemplate = template
    },
    useTemplate() {
      if (this.selectedTemplate) {
        this.$message.success(`使用模板: ${this.selectedTemplate.name}`)
        this.templateLibraryVisible = false
        // 这里可以添加使用模板的逻辑，比如跳转到编辑页面
      }
    },
    editTag(tag) {
      this.$message.info(`编辑标签: ${tag.name}`)
    },
    deleteTag(tag) {
      this.$confirm(`确定要删除标签"${tag.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.tags.findIndex(t => t.id === tag.id)
        if (index > -1) {
          this.tags.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    inviteCollaborator() {
      this.inviteDialogVisible = true
    },
    refreshList() {
      this.$message.success('列表已刷新')
    },
    openReport(report) {
      this.$message.info(`打开协作报告: ${report.title}`)
    },
    manageCollaborators(report) {
      this.$message.info(`管理协作者: ${report.title}`)
    },
    shareReport(report) {
      this.$message.info(`分享报告: ${report.title}`)
    },
    handleCommand(command) {
      const { action, report } = command
      switch (action) {
        case 'copy':
          this.$message.success('链接已复制到剪贴板')
          break
        case 'export':
          this.$message.info(`导出报告: ${report.title}`)
          break
        case 'leave':
          this.leaveCollaboration(report)
          break
      }
    },
    leaveCollaboration(report) {
      this.$confirm(`确定要退出协作报告"${report.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.reports.findIndex(r => r.id === report.id)
        if (index > -1) {
          this.reports.splice(index, 1)
          this.$message.success('已退出协作')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    sendInvite() {
      if (!this.inviteForm.reportId || !this.inviteForm.email || !this.inviteForm.role) {
        this.$message.warning('请填写完整信息')
        return
      }

      // 这里应该调用API发送邀请
      this.$message.success('邀请已发送')
      this.inviteDialogVisible = false
      this.inviteForm = {
        reportId: '',
        email: '',
        role: 'viewer',
        message: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;

  .page-title {
    font-size: 24px;
    color: #303133;
    margin: 0 0 8px 0;
  }

  .page-description {
    color: #909399;
    margin: 0;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .left-actions {
    display: flex;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
  }
}

.collaborate-list {
  .report-item-col {
    margin-bottom: 20px;
  }

  .report-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s;
    height: 280px;
    display: flex;
    flex-direction: column;

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 16px 0 16px;

      .report-icon {
        width: 40px;
        height: 40px;
        background: #E6A23C;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 18px;
      }
    }

    .card-content {
      padding: 16px;
      flex: 1;

      .report-title {
        font-size: 16px;
        color: #303133;
        margin: 0 0 8px 0;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .report-description {
        color: #606266;
        font-size: 14px;
        margin: 0 0 16px 0;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .collaborators {
        .collaborator-avatars {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .more-count {
            font-size: 12px;
            color: #909399;
            margin-left: 8px;
          }
        }

        .last-activity {
          .activity-text {
            font-size: 12px;
            color: #606266;
            display: block;
          }

          .activity-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .card-actions {
      padding: 0 16px 16px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #f0f0f0;
      padding-top: 12px;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .empty-icon {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    color: #303133;
    margin: 0 0 8px 0;
  }

  p {
    color: #909399;
    margin: 0;

    a {
      color: #409EFF;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 模板库抽屉样式
::v-deep .template-library-drawer {
  .el-drawer {
    border-radius: 20px 20px 0 0;

    .el-drawer__header {
      padding: 20px 24px 0;
      margin-bottom: 0;

      .el-drawer__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-drawer__body {
      padding: 20px 24px 24px;
      height: calc(100% - 60px);
      overflow: hidden;
    }
  }
}

.template-library-content {
  display: flex;
  height: 100%;

  .template-sidebar {
    width: 300px;
    border-right: 1px solid #e6e6e6;
    padding: 20px;
    overflow-y: auto;

    .template-search {
      margin-bottom: 20px;
    }

    .category-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
    }

    .template-list {
      .template-item {
        padding: 12px;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409EFF;
          background-color: #f0f9ff;
        }

        &.active {
          border-color: #409EFF;
          background-color: #ecf5ff;
        }

        .template-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .template-desc {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }

  .template-preview {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .preview-content {
      height: 100%;

      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e6e6e6;

        h3 {
          margin: 0;
          font-size: 18px;
          color: #303133;
        }
      }

      .preview-body {
        .template-preview-image {
          text-align: center;

          img {
            max-width: 100%;
            max-height: 600px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .preview-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #909399;

      .empty-icon {
        font-size: 64px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}
</style>
