<template>
  <div class="app-container">
    <div class="page-header">
      <h2 class="page-title">回收站</h2>
      <p class="page-description">管理已删除的报告，支持恢复或永久删除</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-actions">
        <el-button 
          type="danger" 
          icon="el-icon-delete" 
          @click="clearAll"
          :disabled="trashedReports.length === 0"
        >
          清空回收站
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshList">刷新</el-button>
      </div>
      <div class="right-actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索已删除的报告..."
          prefix-icon="el-icon-search"
          style="width: 300px;"
          clearable
        />
        <el-select v-model="filterDays" placeholder="删除时间" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="7天内" value="7" />
          <el-option label="30天内" value="30" />
          <el-option label="90天内" value="90" />
        </el-select>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedReports.length > 0" class="batch-actions">
      <span class="selected-info">已选择 {{ selectedReports.length }} 项</span>
      <el-button type="primary" size="small" @click="batchRestore">批量恢复</el-button>
      <el-button type="danger" size="small" @click="batchDelete">批量删除</el-button>
    </div>

    <!-- 回收站列表 -->
    <div class="trash-list">
      <el-table 
        :data="filteredReports" 
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column width="60">
          <template slot-scope="scope">
            <div class="report-icon">
              <i class="el-icon-document-delete"></i>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="报告名称" min-width="200">
          <template slot-scope="scope">
            <div class="report-info">
              <div class="report-title">{{ scope.row.title }}</div>
              <div class="report-description">{{ scope.row.description }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="originalPath" label="原路径" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="deleteTime" label="删除时间" width="150" />
        
        <el-table-column prop="deleteBy" label="删除人" width="100" />
        
        <el-table-column label="剩余天数" width="100">
          <template slot-scope="scope">
            <span :class="{ 'warning': getRemainingDays(scope.row.deleteTime) <= 7 }">
              {{ getRemainingDays(scope.row.deleteTime) }} 天
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="size" label="大小" width="100" />
        
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="restoreReport(scope.row)">恢复</el-button>
            <el-button type="text" size="small" @click="previewReport(scope.row)">预览</el-button>
            <el-button type="text" size="small" @click="permanentDelete(scope.row)">永久删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredReports.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="el-icon-delete"></i>
      </div>
      <h3>回收站为空</h3>
      <p>回收站中没有任何报告</p>
    </div>

    <!-- 预览对话框 -->
    <el-dialog title="报告预览" :visible.sync="previewDialogVisible" width="80%">
      <div class="preview-content">
        <div class="preview-header">
          <h3>{{ currentPreviewReport.title }}</h3>
          <p>{{ currentPreviewReport.description }}</p>
          <div class="preview-meta">
            <span>删除时间：{{ currentPreviewReport.deleteTime }}</span>
            <span>删除人：{{ currentPreviewReport.deleteBy }}</span>
            <span>原路径：{{ currentPreviewReport.originalPath }}</span>
          </div>
        </div>
        <div class="preview-body">
          <div class="preview-placeholder">
            <i class="el-icon-document"></i>
            <p>报告内容预览</p>
            <p class="preview-note">这里显示报告的预览内容</p>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="restoreFromPreview">恢复此报告</el-button>
      </div>
    </el-dialog>

    <!-- 恢复位置选择对话框 -->
    <el-dialog title="选择恢复位置" :visible.sync="restoreDialogVisible" width="500px">
      <el-form :model="restoreForm" label-width="100px">
        <el-form-item label="恢复到">
          <el-radio-group v-model="restoreForm.restoreType">
            <el-radio label="original">原位置</el-radio>
            <el-radio label="custom">自定义位置</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="restoreForm.restoreType === 'custom'" label="目标文件夹">
          <el-select v-model="restoreForm.targetFolder" placeholder="选择文件夹" style="width: 100%;">
            <el-option label="我的报告" value="/" />
            <el-option label="数据分析" value="/analysis" />
            <el-option label="财务报告" value="/finance" />
            <el-option label="市场研究" value="/market" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="重命名">
          <el-input v-model="restoreForm.newName" placeholder="可选，重新命名报告" />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="restoreDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmRestore">确认恢复</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ReportTrash',
  data() {
    return {
      searchKeyword: '',
      filterDays: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedReports: [],
      previewDialogVisible: false,
      restoreDialogVisible: false,
      currentPreviewReport: {},
      currentRestoreReport: null,
      restoreForm: {
        restoreType: 'original',
        targetFolder: '/',
        newName: ''
      },
      trashedReports: [
        {
          id: 1,
          title: '过期的销售报告',
          description: '2023年销售数据分析报告',
          originalPath: '/我的报告/销售分析',
          deleteTime: '2024-01-10 14:30',
          deleteBy: '张三',
          size: '2.5MB',
          type: 'report'
        },
        {
          id: 2,
          title: '临时用户分析',
          description: '临时用户行为分析草稿',
          originalPath: '/我的报告/用户研究',
          deleteTime: '2024-01-08 16:20',
          deleteBy: '李四',
          size: '1.8MB',
          type: 'report'
        },
        {
          id: 3,
          title: '旧版财务月报',
          description: '已被新版本替代的财务报告',
          originalPath: '/我的报告/财务报告',
          deleteTime: '2024-01-05 09:15',
          deleteBy: '王五',
          size: '3.2MB',
          type: 'report'
        }
      ]
    }
  },
  computed: {
    filteredReports() {
      let filtered = this.trashedReports
      
      // 时间筛选
      if (this.filterDays) {
        const daysAgo = new Date()
        daysAgo.setDate(daysAgo.getDate() - parseInt(this.filterDays))
        filtered = filtered.filter(report => 
          new Date(report.deleteTime) >= daysAgo
        )
      }
      
      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(report => 
          report.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          report.description.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          report.originalPath.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      
      this.total = filtered.length
      return filtered
    }
  },
  methods: {
    getRemainingDays(deleteTime) {
      const deleteDate = new Date(deleteTime)
      const expireDate = new Date(deleteDate)
      expireDate.setDate(expireDate.getDate() + 30) // 30天后永久删除
      
      const now = new Date()
      const diffTime = expireDate - now
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      return Math.max(0, diffDays)
    },
    handleSelectionChange(selection) {
      this.selectedReports = selection
    },
    restoreReport(report) {
      this.currentRestoreReport = report
      this.restoreForm = {
        restoreType: 'original',
        targetFolder: '/',
        newName: ''
      }
      this.restoreDialogVisible = true
    },
    confirmRestore() {
      const report = this.currentRestoreReport
      const index = this.trashedReports.findIndex(r => r.id === report.id)
      
      if (index > -1) {
        this.trashedReports.splice(index, 1)
        this.$message.success(`报告"${report.title}"已恢复`)
      }
      
      this.restoreDialogVisible = false
    },
    restoreFromPreview() {
      this.previewDialogVisible = false
      this.restoreReport(this.currentPreviewReport)
    },
    previewReport(report) {
      this.currentPreviewReport = report
      this.previewDialogVisible = true
    },
    permanentDelete(report) {
      this.$confirm(`确定要永久删除"${report.title}"吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        const index = this.trashedReports.findIndex(r => r.id === report.id)
        if (index > -1) {
          this.trashedReports.splice(index, 1)
          this.$message.success('报告已永久删除')
        }
      }).catch(() => {
        // 用户取消
      })
    },
    batchRestore() {
      this.$confirm(`确定要恢复选中的 ${this.selectedReports.length} 个报告吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectedReports.forEach(report => {
          const index = this.trashedReports.findIndex(r => r.id === report.id)
          if (index > -1) {
            this.trashedReports.splice(index, 1)
          }
        })
        this.$message.success(`已恢复 ${this.selectedReports.length} 个报告`)
        this.selectedReports = []
      }).catch(() => {
        // 用户取消
      })
    },
    batchDelete() {
      this.$confirm(`确定要永久删除选中的 ${this.selectedReports.length} 个报告吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.selectedReports.forEach(report => {
          const index = this.trashedReports.findIndex(r => r.id === report.id)
          if (index > -1) {
            this.trashedReports.splice(index, 1)
          }
        })
        this.$message.success(`已永久删除 ${this.selectedReports.length} 个报告`)
        this.selectedReports = []
      }).catch(() => {
        // 用户取消
      })
    },
    clearAll() {
      this.$confirm('确定要清空回收站吗？所有报告将被永久删除，此操作不可恢复！', '警告', {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.trashedReports = []
        this.$message.success('回收站已清空')
      }).catch(() => {
        // 用户取消
      })
    },
    refreshList() {
      this.$message.success('列表已刷新')
    },
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  
  .page-title {
    font-size: 24px;
    color: #303133;
    margin: 0 0 8px 0;
  }
  
  .page-description {
    color: #909399;
    margin: 0;
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .left-actions {
    display: flex;
    gap: 10px;
  }
  
  .right-actions {
    display: flex;
    align-items: center;
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  
  .selected-info {
    color: #1890ff;
    font-weight: 500;
  }
}

.trash-list {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .report-icon {
    width: 32px;
    height: 32px;
    background: #909399;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
  }
  
  .report-info {
    .report-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .report-description {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;
    }
  }
  
  .warning {
    color: #e6a23c;
    font-weight: 500;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .empty-icon {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }
  
  h3 {
    color: #303133;
    margin: 0 0 8px 0;
  }
  
  p {
    color: #909399;
    margin: 0;
  }
}

.preview-content {
  .preview-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 16px;
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0 0 12px 0;
      color: #606266;
    }
    
    .preview-meta {
      display: flex;
      gap: 20px;
      font-size: 12px;
      color: #909399;
    }
  }
  
  .preview-body {
    .preview-placeholder {
      text-align: center;
      padding: 40px;
      background: #f5f7fa;
      border-radius: 4px;
      
      i {
        font-size: 48px;
        color: #dcdfe6;
        margin-bottom: 16px;
        display: block;
      }
      
      p {
        margin: 0;
        color: #909399;
        
        &.preview-note {
          font-size: 12px;
          margin-top: 8px;
        }
      }
    }
  }
}
</style>
