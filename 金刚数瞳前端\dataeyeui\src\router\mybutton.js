import Layout from '@/layout'

const mybuttonRouter = {
  path: '/mybutton',
  component: Layout,
  redirect: 'noRedirect',
  name: 'My<PERSON>utt<PERSON>',
  meta: {
    title: '我的',
    icon: 'user'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/mybutton/index'),
      name: 'MyButtonIndex',
      meta: { title: '设置', icon: 'dashboard' }
    }
  ]
}

export default mybuttonRouter
