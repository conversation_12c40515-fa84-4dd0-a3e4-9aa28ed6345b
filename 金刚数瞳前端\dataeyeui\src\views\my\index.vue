<template>
  <div class="app-container">
    <div class="my-container">
      <!-- 左侧菜单 -->
      <div class="left-menu">
        <div class="menu-item" :class="{ active: activeMenu === 'address' }" @click="activeMenu = 'address'">
          <i class="el-icon-data-analysis"></i>
          <span>指标告警(0)</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'f2code' }" @click="activeMenu = 'f2code'">
          <i class="el-icon-document"></i>
          <span>卡片提醒(0)</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'questions' }" @click="activeMenu = 'questions'">
          <i class="el-icon-question"></i>
          <span>关注的问题(0)</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'edit' }" @click="activeMenu = 'edit'">
          <i class="el-icon-star-off"></i>
          <span>保存的卡片(0)</span>
        </div>
        <div class="menu-item" :class="{ active: activeMenu === 'tags' }" @click="activeMenu = 'tags'">
          <i class="el-icon-time"></i>
          <span>搜索历史(11)</span>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="content-area">
        <!-- 默认显示的空内容（指标告罄） -->
        <template v-if="activeMenu !== 'tags' && activeMenu !== 'edit' && activeMenu !== 'f2code' && activeMenu !== 'questions'">
          <div class="header">
            <el-button type="primary" size="small" class="add-button">新建</el-button>
          </div>

          <div class="empty-content">
            <div class="empty-icon">
              <i class="el-icon-folder-opened"></i>
            </div>
            <div class="empty-text">暂无告罄</div>
          </div>
        </template>

        <!-- 保存的卡片内容 -->
        <template v-if="activeMenu === 'edit'">
          <div class="saved-cards-header">
            <div class="title">保存的卡片</div>
            <div class="search-box">
              <el-input
                placeholder="在保存的卡片中查找"
                prefix-icon="el-icon-search"
                v-model="cardSearchKeyword"
                clearable>
              </el-input>
            </div>
          </div>

          <div class="empty-content">
            <div class="empty-icon">
              <i class="el-icon-folder-opened"></i>
            </div>
            <div class="empty-text">暂未保存任何卡片，尝试去<a href="javascript:;" class="link-text">探索数据</a>吧</div>
          </div>
        </template>

        <!-- 卡片体现内容 -->
        <template v-if="activeMenu === 'f2code'">
          <div class="saved-cards-header">
            <div class="title">卡片记录</div>
          </div>

          <div class="card-table-container">
            <el-table
              :data="cardShowList"
              style="width: 100%"
              border>
              <el-table-column
                prop="name"
                label="监控卡片"
                min-width="180">
              </el-table-column>
              <el-table-column
                prop="createTime"
                label="触发条件"
                width="180">
              </el-table-column>
              <el-table-column
                prop="updateTime"
                label="计算频率"
                width="180">
              </el-table-column>
              <el-table-column
                prop="creator"
                label="提醒接受人"
                width="120">
              </el-table-column>
              <el-table-column
                prop="lastViewTime"
                label="最近触发时间"
                width="180">
              </el-table-column>
              <el-table-column
                label="操作"
                width="120">
                <template slot-scope="scope">
                  <el-button type="text" size="small">查看</el-button>
                  <el-button type="text" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <div v-if="cardShowList.length === 0" class="empty-table">
              <div class="empty-text">暂无数据</div>
            </div>
          </div>
        </template>

        <!-- 关注的问题内容 -->
        <template v-if="activeMenu === 'questions'">
          <div class="saved-cards-header">
            <div class="title">关注的问题</div>
            <div class="search-box">
              <el-input
                placeholder="在关注的问题中查找"
                prefix-icon="el-icon-search"
                v-model="questionSearchKeyword"
                clearable>
              </el-input>
            </div>
          </div>

          <div class="empty-content">
            <div class="empty-icon">
              <i class="el-icon-question"></i>
            </div>
            <div class="empty-text">暂未关注任何问题，尝试去<a href="javascript:;" class="link-text">探索数据</a>吧</div>
          </div>
        </template>

        <!-- 搜索历史内容 -->
        <template v-if="activeMenu === 'tags'">
          <div class="search-history-header">
            <div class="user-info">
              <span class="username">程序员小吴</span>
              <span class="user-desc">(可通过以下方式搜索)</span>
            </div>
            <div class="search-box">
              <el-input
                placeholder="在搜索历史中查找"
                prefix-icon="el-icon-search"
                v-model="searchKeyword"
                clearable>
              </el-input>
            </div>
          </div>

          <div class="search-history-list">
            <el-table
              :data="historyList"
              style="width: 100%"
              :show-header="false">
              <el-table-column width="70%">
                <template slot-scope="scope">
                  <div class="history-item">{{ scope.row.content }}</div>
                </template>
              </el-table-column>
              <el-table-column width="30%" align="right">
                <template slot-scope="scope">
                  <div class="history-time">{{ scope.row.time }}</div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="prev, pager, next, sizes, jumper"
              :total="total">
            </el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MyCenter',
  data() {
    return {
      activeMenu: 'address', // 默认选中第一个菜单项
      searchKeyword: '',
      cardSearchKeyword: '',
      questionSearchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      total: 110,
      cardList: [],
      cardShowList: [],
      historyList: [
        { content: '金融', time: '2023-04-09 23:05:24' },
        { content: '近期国债收益率10年期国债收益率', time: '2023-04-09 23:52:36' },
        { content: '上海房产价格', time: '2023-04-08 14:44:27' },
        { content: '今日汇率与昨日汇率', time: '2023-04-07 15:04:37' },
        { content: '今日汇率与昨日汇率与过去一周汇率趋势与走势图', time: '2023-04-07 14:21:19' },
        { content: '产品销售量', time: '2023-04-07 13:50:10' },
        { content: '最近的股票市场行情', time: '2023-04-07 13:46:32' },
        { content: '上海房产价格走势如何', time: '2023-04-07 13:45:11' },
        { content: '今日汇率与昨日汇率与过去一周汇率趋势与走势图', time: '2023-04-07 13:45:51' },
        { content: '金融市场走势', time: '2023-04-07 09:52:21' }
      ]
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
      // 这里可以添加获取数据的逻辑
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      // 这里可以添加获取数据的逻辑
    }
  }
}
</script>

<style scoped>
.app-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.my-container {
  display: flex;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: calc(100vh - 40px);
}

/* 左侧菜单样式 */
.left-menu {
  width: 200px;
  border-right: 1px solid #ebeef5;
  padding: 20px 0;
}

.menu-item {
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #606266;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item.active {
  color: #409EFF;
  background-color: #ecf5ff;
}

.menu-item i {
  margin-right: 8px;
  font-size: 16px;
}

/* 右侧内容区样式 */
.content-area {
  flex: 1;
  padding: 20px;
  position: relative;
}

.header {
  margin-bottom: 20px;
}

.add-button {
  border-radius: 20px;
  padding: 8px 20px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.empty-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.empty-icon i {
  font-size: 24px;
  color: #909399;
}

.empty-text {
  font-size: 14px;
}

/* 搜索历史样式 */
.search-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
}

.user-desc {
  font-size: 14px;
  color: #909399;
}

.search-box {
  width: 300px;
}

.search-history-list {
  margin-bottom: 20px;
}

.history-item {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  padding: 10px 0;
}

.history-time {
  font-size: 14px;
  color: #909399;
  padding: 10px 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 保存的卡片样式 */
.saved-cards-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.card-table-container {
  position: relative;
  min-height: 300px;
}

.empty-table {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
}

.link-text {
  color: #409EFF;
  text-decoration: none;
}

.link-text:hover {
  text-decoration: underline;
}
</style>
