{% set pkField = pkColumn.python_field %}
{% set pk_field = pkColumn.python_field | camel_to_snake %}
{% for column in columns %}
{% if column.python_field == "createTime" %}
from datetime import datetime
{% endif %}
{% endfor %}
from fastapi import APIRouter, Depends, Form, Request
from pydantic_validation_decorator import <PERSON><PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from {{ packageName }}.service.{{ businessName }}_service import {{ BusinessName }}Service
from {{ packageName }}.entity.vo.{{ businessName }}_vo import Delete{{ BusinessName }}Model, {{ BusinessName }}Model, {{ BusinessName }}PageQueryModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


{{ businessName }}Controller = APIRouter(prefix='/{{ moduleName }}/{{ businessName }}', dependencies=[Depends(LoginService.get_current_user)])


@{{ businessName }}Controller.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('{{ permissionPrefix }}:list'))]
)
async def get_{{ moduleName }}_{{ businessName }}_list(
    request: Request,
    {% if table.crud or table.sub %}{{ businessName }}_page_query{% elif table.tree %}{{ businessName }}_query{% endif %}: {{ BusinessName }}PageQueryModel = Depends({{ BusinessName }}PageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    {% if table.crud or table.sub %}
    # 获取分页数据
    {{ businessName }}_page_query_result = await {{ BusinessName }}Service.get_{{ businessName }}_list_services(query_db, {{ businessName }}_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content={{ businessName }}_page_query_result)
    {% elif table.tree %}
    {{ businessName }}_query_result = await {{ BusinessName }}Service.get_{{ businessName }}_list_services(query_db, {{ businessName }}_query)
    logger.info('获取成功')

    return ResponseUtil.success(data={{ businessName }}_query_result)
    {% endif %}


@{{ businessName }}Controller.post('', dependencies=[Depends(CheckUserInterfaceAuth('{{ permissionPrefix }}:add'))])
@ValidateFields(validate_model='add_{{ businessName }}')
@Log(title='{{ functionName }}', business_type=BusinessType.INSERT)
async def add_{{ moduleName }}_{{ businessName }}(
    request: Request,
    add_{{ businessName }}: {{ BusinessName }}Model,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    {% for column in columns %}
    {% if column.python_field == "createBy" %}
    add_{{ businessName }}.create_by = current_user.user.user_name
    {% elif column.python_field == "createTime" %}
    add_{{ businessName }}.create_time = datetime.now()
    {% elif column.python_field == "updateBy" %}
    add_{{ businessName }}.update_by = current_user.user.user_name
    {% elif column.python_field == "updateTime" %}
    add_{{ businessName }}.update_time = datetime.now()
    {% endif %}
    {% endfor %}
    add_{{ businessName }}_result = await {{ BusinessName }}Service.add_{{ businessName }}_services(query_db, add_{{ businessName }})
    logger.info(add_{{ businessName }}_result.message)

    return ResponseUtil.success(msg=add_{{ businessName }}_result.message)


@{{ businessName }}Controller.put('', dependencies=[Depends(CheckUserInterfaceAuth('{{ permissionPrefix }}:edit'))])
@ValidateFields(validate_model='edit_{{ businessName }}')
@Log(title='{{ functionName }}', business_type=BusinessType.UPDATE)
async def edit_{{ moduleName }}_{{ businessName }}(
    request: Request,
    edit_{{ businessName }}: {{ BusinessName }}Model,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    edit_{{ businessName }}.update_by = current_user.user.user_name
    edit_{{ businessName }}.update_time = datetime.now()
    edit_{{ businessName }}_result = await {{ BusinessName }}Service.edit_{{ businessName }}_services(query_db, edit_{{ businessName }})
    logger.info(edit_{{ businessName }}_result.message)

    return ResponseUtil.success(msg=edit_{{ businessName }}_result.message)


@{{ businessName }}Controller.delete('/{% raw %}{{% endraw %}{{ pk_field }}s{% raw %}}{% endraw %}', dependencies=[Depends(CheckUserInterfaceAuth('{{ permissionPrefix }}:remove'))])
@Log(title='{{ functionName }}', business_type=BusinessType.DELETE)
async def delete_{{ moduleName }}_{{ businessName }}(request: Request, {{ pk_field }}s: str, query_db: AsyncSession = Depends(get_db)):
    delete_{{ businessName }} = Delete{{ BusinessName }}Model({{ pkField }}s={{ pk_field }}s)
    delete_{{ businessName }}_result = await {{ BusinessName }}Service.delete_{{ businessName }}_services(query_db, delete_{{ businessName }})
    logger.info(delete_{{ businessName }}_result.message)

    return ResponseUtil.success(msg=delete_{{ businessName }}_result.message)


@{{ businessName }}Controller.get(
    '/{% raw %}{{% endraw %}{{ pk_field }}{% raw %}}{% endraw %}', response_model={{ BusinessName }}Model, dependencies=[Depends(CheckUserInterfaceAuth('{{ permissionPrefix }}:query'))]
)
async def query_detail_{{ moduleName }}_{{ businessName }}(request: Request, {{ pk_field }}: int, query_db: AsyncSession = Depends(get_db)):
    {{ businessName }}_detail_result = await {{ BusinessName }}Service.{{ businessName }}_detail_services(query_db, {{ pk_field }})
    logger.info(f'获取{{ pk_field }}为{% raw %}{{% endraw %}{{ pk_field }}{% raw %}}{% endraw %}的信息成功')

    return ResponseUtil.success(data={{ businessName }}_detail_result)


@{{ businessName }}Controller.post('/export', dependencies=[Depends(CheckUserInterfaceAuth('{{ permissionPrefix }}:export'))])
@Log(title='{{ functionName }}', business_type=BusinessType.EXPORT)
async def export_{{ moduleName }}_{{ businessName }}_list(
    request: Request,
    {{ businessName }}_page_query: {{ BusinessName }}PageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取全量数据
    {{ businessName }}_query_result = await {{ BusinessName }}Service.get_{{ businessName }}_list_services(query_db, {{ businessName }}_page_query, is_page=False)
    {{ businessName }}_export_result = await {{ BusinessName }}Service.export_{{ businessName }}_list_services({% if dicts %}request, {% endif %}{{ businessName }}_query_result)
    logger.info('导出成功')

    return ResponseUtil.streaming(data=bytes2file_response({{ businessName }}_export_result))
