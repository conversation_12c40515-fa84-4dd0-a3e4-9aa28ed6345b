<template>
  <div class="dataeye-container">
    <!-- AI智能问数区域 -->
    <div class="ai-qa-section">
      <div class="qa-container">
        <h2 class="section-title">AI智能问数</h2>
        <div class="qa-content">
          <!-- 问题输入框 -->
          <div class="question-input">
            <el-input
              v-model="question"
              placeholder="请输入您的问题，Shift + Enter 换行"
              class="input-area"
              @keyup.enter.native="handleSearch"
            >
              <el-button
                slot="append"
                icon="el-icon-s-promotion"
                @click="handleSearch"
              ></el-button>
            </el-input>
          </div>

          <!-- 历史搜索区域 -->
          <div class="history-section">
            <div class="history-header">
              <div class="history-search-title">历史搜索</div>
              <el-button
                v-if="searchHistory.length > 0"
                type="text"
                size="small"
                @click="clearHistory"
                class="clear-btn"
              >
                清空历史
              </el-button>
            </div>

            <!-- 搜索历史标签 -->
            <div v-if="searchHistory.length > 0" class="history-tags">
              <el-tag
                v-for="(item, index) in displayHistory"
                :key="index"
                class="history-tag"
                closable
                @click="selectQuestion(item.content)"
                @close="removeHistoryItem(index)"
              >
                {{ item.content }}
              </el-tag>
              <el-button
                v-if="searchHistory.length > maxDisplayHistory"
                type="text"
                size="small"
                @click="showAllHistory = !showAllHistory"
                class="toggle-btn"
              >
                {{ showAllHistory ? '收起' : `查看全部(${searchHistory.length})` }}
              </el-button>
            </div>

            <!-- 常用问题标签 -->
            <div class="common-questions-section">
              <div class="section-subtitle">常用问题</div>
              <div class="question-tags">
                <el-tag
                  v-for="(tag, index) in commonQuestions"
                  :key="index"
                  class="question-tag"
                  @click="selectQuestion(tag)"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据仪表板区域 -->
    <div class="dashboard-section">
      <!-- 数据卡片区域 -->
      <div class="data-cards-section">
        <!-- 营业额卡片 -->
        <div class="data-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-s-data"></i>
              营业额/万元
            </div>
            <div class="card-actions">
              <el-button type="text" size="mini" icon="el-icon-more"></el-button>
            </div>
          </div>
          <div class="card-content">
            <div class="main-value">165.32</div>
            <div class="sub-info">
              <span class="date-info">2024-01-01 至 14:23</span>
              <span class="trend-info positive">
                <i class="el-icon-top"></i>
                同比 +14.5%
              </span>
            </div>
          </div>
        </div>

        <!-- 门店营业额前十名卡片 -->
        <div class="data-card wide-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-s-shop"></i>
              门店营业额前十名
            </div>
            <div class="card-actions">
              <el-button type="text" size="mini" icon="el-icon-refresh"></el-button>
              <el-button type="text" size="mini" icon="el-icon-download"></el-button>
              <el-button type="text" size="mini" icon="el-icon-more"></el-button>
            </div>
          </div>
          <div class="card-content">
            <div class="date-info">2024-01-01 至 17:31</div>
            <div class="chart-container">
              <div ref="storeChart" class="chart" style="height: 300px;"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 智能洞察区域 -->
      <div class="insights-section">
        <div class="insights-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-lightbulb"></i>
              智能洞察
            </div>
            <div class="card-actions">
              <el-button type="text" size="mini">智能分析</el-button>
            </div>
          </div>
          <div class="card-content">
            <div class="insight-item">
              <div class="insight-icon">
                <i class="el-icon-warning-outline"></i>
              </div>
              <div class="insight-content">
                <div class="insight-title">营业额下降提醒</div>
                <div class="insight-desc">本周营业额相比上周下降了8.2%，建议关注门店运营情况</div>
              </div>
            </div>
            <div class="insight-item">
              <div class="insight-icon success">
                <i class="el-icon-success"></i>
              </div>
              <div class="insight-content">
                <div class="insight-title">会员增长良好</div>
                <div class="insight-desc">新增会员数量环比增长15.3%，会员活跃度持续提升</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cache from '@/plugins/cache'
import * as echarts from 'echarts'

export default {
  name: 'DataeyeIndex',
  data() {
    return {
      activeIndex: '1',
      question: '',
      searchHistory: [],
      showAllHistory: false,
      maxDisplayHistory: 8, // 默认显示的历史记录数量
      commonQuestions: [
        '上季度营业额',
        '今年Q1营业额每天',
        '去年营业额前十的门店同比情况',
        '今年Q1营业额每天品牌会员客流趋势上海北京',
        '营业额每月',
        '营业额降序的5个品牌',
        '上季度营业额目标是多少',
        '去年的营业额',
        '去年门店是(三里屯)品牌是(耐克)门店面积营业额'
      ],
      storeChart: null,
      chartData: {
        stores: ['三里屯店', '王府井店', '西单店', '朝阳店', '海淀店', '丰台店', '石景山店', '通州店', '昌平店', '大兴店'],
        revenue: [6892.192, 5482.192, 5200.007, 4702.477, 4200.477, 3962.577, 3425.547, 2894.477, 2638.849, 2114.669],
        growth: [4.7, 3.2, -2.5, 1.8, -0.5, 2.1, -1.2, 0.8, 1.5, -0.3]
      }
    }
  },
  computed: {
    displayHistory() {
      if (this.showAllHistory) {
        return this.searchHistory
      }
      return this.searchHistory.slice(0, this.maxDisplayHistory)
    }
  },
  mounted() {
    this.loadSearchHistory()
    this.$nextTick(() => {
      this.initStoreChart()
    })
  },
  beforeDestroy() {
    if (this.storeChart) {
      this.storeChart.dispose()
    }
  },
  methods: {
    // 处理搜索
    handleSearch() {
      const query = this.question.trim()
      if (!query) {
        this.$message.warning('请输入搜索内容')
        return
      }

      // 添加到搜索历史
      this.addToHistory(query)

      // 这里可以添加实际的搜索逻辑
      this.$message.success(`正在搜索: ${query}`)

      // 清空输入框
      this.question = ''
    },

    // 选择问题
    selectQuestion(question) {
      this.question = question
      // 可以选择是否立即搜索
      // this.handleSearch()
    },

    // 添加到搜索历史
    addToHistory(query) {
      // 检查是否已存在
      const existingIndex = this.searchHistory.findIndex(item => item.content === query)

      if (existingIndex !== -1) {
        // 如果已存在，移除旧的记录
        this.searchHistory.splice(existingIndex, 1)
      }

      // 添加到开头
      this.searchHistory.unshift({
        content: query,
        time: new Date().toLocaleString()
      })

      // 限制历史记录数量（最多保存50条）
      if (this.searchHistory.length > 50) {
        this.searchHistory = this.searchHistory.slice(0, 50)
      }

      // 保存到本地存储
      this.saveSearchHistory()
    },

    // 移除单个历史记录
    removeHistoryItem(index) {
      const actualIndex = this.showAllHistory ? index : index
      this.searchHistory.splice(actualIndex, 1)
      this.saveSearchHistory()
    },

    // 清空历史记录
    clearHistory() {
      this.$confirm('确定要清空所有搜索历史吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.searchHistory = []
        this.showAllHistory = false
        this.saveSearchHistory()
        this.$message.success('搜索历史已清空')
      }).catch(() => {
        // 用户取消
      })
    },

    // 保存搜索历史到本地存储
    saveSearchHistory() {
      cache.local.setJSON('dataeye_search_history', this.searchHistory)
    },

    // 从本地存储加载搜索历史
    loadSearchHistory() {
      const history = cache.local.getJSON('dataeye_search_history')
      if (history && Array.isArray(history)) {
        this.searchHistory = history
      }
    },

    // 初始化门店营业额图表
    initStoreChart() {
      if (!this.$refs.storeChart) return

      this.storeChart = echarts.init(this.$refs.storeChart)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          data: ['营业额', '增长率'],
          top: 10
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartData.stores,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              rotate: 45,
              fontSize: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '营业额(万)',
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '增长率(%)',
            position: 'right',
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '营业额',
            type: 'bar',
            data: this.chartData.revenue,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2378f7' },
                  { offset: 0.7, color: '#2378f7' },
                  { offset: 1, color: '#83bff6' }
                ])
              }
            }
          },
          {
            name: '增长率',
            type: 'line',
            yAxisIndex: 1,
            data: this.chartData.growth,
            itemStyle: {
              color: '#ffc658'
            },
            lineStyle: {
              color: '#ffc658',
              width: 2
            }
          }
        ]
      }

      this.storeChart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        if (this.storeChart) {
          this.storeChart.resize()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dataeye-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .ai-qa-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .section-title {
      text-align: center;
      font-size: 20px;
      color: #303133;
      margin-bottom: 20px;
    }

    .qa-content {
      .question-input {
        margin-bottom: 20px;

        .input-area {
          ::v-deep .el-input__inner {
            border-radius: 20px;
            height: 40px;
            line-height: 40px;
          }

          ::v-deep .el-input-group__append {
            border-radius: 0 20px 20px 0;
            border-left: none;

            .el-button {
              border-radius: 0 20px 20px 0;
              height: 38px;
              width: 40px;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .history-section {
        .history-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;

          .history-search-title {
            font-size: 14px;
            color: #909399;
            font-weight: 500;
          }

          .clear-btn {
            color: #409EFF;
            padding: 0;
            font-size: 12px;

            &:hover {
              color: #66b1ff;
            }
          }
        }

        .history-tags {
          margin-bottom: 20px;

          .history-tag {
            background-color: #e8f4ff;
            border: 1px solid #b3d8ff;
            color: #409EFF;
            border-radius: 15px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
            padding: 6px 12px;
            font-size: 13px;

            &:hover {
              background-color: #409EFF;
              color: #fff;
              border-color: #409EFF;
            }

            ::v-deep .el-tag__close {
              color: #409EFF;

              &:hover {
                background-color: #fff;
                color: #f56c6c;
              }
            }
          }

          .toggle-btn {
            color: #909399;
            padding: 0;
            font-size: 12px;
            margin-left: 8px;

            &:hover {
              color: #409EFF;
            }
          }
        }

        .common-questions-section {
          .section-subtitle {
            font-size: 14px;
            color: #909399;
            margin-bottom: 10px;
            font-weight: 500;
          }

          .question-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .question-tag {
              background-color: #f0f2f5;
              border: 1px solid #dcdfe6;
              border-radius: 15px;
              color: #606266;
              cursor: pointer;
              padding: 6px 12px;
              font-size: 13px;
              transition: all 0.3s;

              &:hover {
                background-color: #409EFF;
                color: #fff;
                border-color: #409EFF;
              }
            }
          }
        }
      }
    }
  }

  // 数据仪表板样式
  .dashboard-section {
    margin-top: 20px;

    .data-cards-section {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 20px;
      margin-bottom: 20px;

      .data-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;

        &.wide-card {
          grid-column: span 1;
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid #f0f0f0;
          background-color: #fafafa;

          .card-title {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            color: #303133;

            i {
              margin-right: 8px;
              color: #409EFF;
            }
          }

          .card-actions {
            display: flex;
            gap: 4px;

            .el-button {
              padding: 4px 8px;
              color: #909399;

              &:hover {
                color: #409EFF;
              }
            }
          }
        }

        .card-content {
          padding: 20px;

          .main-value {
            font-size: 32px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
          }

          .sub-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .date-info {
              font-size: 12px;
              color: #909399;
            }

            .trend-info {
              font-size: 12px;
              display: flex;
              align-items: center;

              &.positive {
                color: #67C23A;
              }

              &.negative {
                color: #F56C6C;
              }

              i {
                margin-right: 2px;
              }
            }
          }

          .date-info {
            font-size: 12px;
            color: #909399;
            margin-bottom: 16px;
          }

          .chart-container {
            .chart {
              width: 100%;
            }
          }
        }
      }
    }

    .insights-section {
      .insights-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid #f0f0f0;
          background-color: #fafafa;

          .card-title {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            color: #303133;

            i {
              margin-right: 8px;
              color: #E6A23C;
            }
          }

          .card-actions {
            .el-button {
              padding: 4px 12px;
              font-size: 12px;
              color: #409EFF;

              &:hover {
                background-color: #ecf5ff;
              }
            }
          }
        }

        .card-content {
          padding: 20px;

          .insight-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .insight-icon {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              background-color: #FDF6EC;
              color: #E6A23C;

              &.success {
                background-color: #F0F9FF;
                color: #67C23A;
              }

              i {
                font-size: 16px;
              }
            }

            .insight-content {
              flex: 1;

              .insight-title {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
              }

              .insight-desc {
                font-size: 12px;
                color: #606266;
                line-height: 1.5;
              }
            }
          }
        }
      }
    }
  }
}
</style>
